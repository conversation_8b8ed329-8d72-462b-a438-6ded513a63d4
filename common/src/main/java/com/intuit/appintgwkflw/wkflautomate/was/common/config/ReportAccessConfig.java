package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import java.util.Arrays;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for report access validation settings.
 * This class contains configuration for validating report access across different realms/companies.
 * 
 * Configuration example:
 * report-access-validation:
 *   enabled: true
 *   baseUrl: https://quickbooks-api.intuit.com
 *   reportValidationEndpoint: /v3/companyinfo/{realmId}/reports/{reportId}/validate
 *   timeoutMs: 5000
 *   maxRetries: 3
 *   supportedReportTypes:
 *     - invoice
 *     - bill
 *     - payment
 *     - estimate
 */
@Configuration
@ConfigurationProperties(prefix = "report-access-validation")
@Getter
@Setter
public class ReportAccessConfig {
    
    /**
     * Flag to enable/disable report access validation.
     * When disabled, all report access checks will be bypassed.
     */
    private boolean enabled = true;
    
    /**
     * Base URL for the QuickBooks API or report validation service
     */
    private String baseUrl;
    
    /**
     * Endpoint for validating report access.
     * Should support placeholders for realmId and reportId
     */
    private String reportValidationEndpoint = "/v3/companyinfo/{realmId}/reports/{reportId}/validate";
    
    /**
     * Timeout in milliseconds for report validation API calls
     */
    private int timeoutMs = 5000;
    
    /**
     * Maximum number of retries for failed API calls
     */
    private int maxRetries = 3;
    
    /**
     * List of supported report types that require validation
     */
    private List<String> supportedReportTypes = Arrays.asList(
        "invoice", 
        "bill", 
        "payment", 
        "estimate", 
        "customer", 
        "vendor",
        "item",
        "account"
    );
    
    /**
     * Feature flag to enable batch validation for multiple reports
     */
    private boolean batchValidationEnabled = true;
    
    /**
     * Maximum number of reports that can be validated in a single batch request
     */
    private int maxBatchSize = 50;
    
    /**
     * Cache TTL in seconds for report access validation results
     */
    private int cacheTtlSeconds = 300; // 5 minutes
    
    /**
     * Flag to enable caching of validation results
     */
    private boolean cachingEnabled = true;
    
    /**
     * Checks if the given report type is supported for validation
     * 
     * @param reportType the report type to check
     * @return true if the report type is supported, false otherwise
     */
    public boolean isSupportedReportType(String reportType) {
        return supportedReportTypes.contains(reportType.toLowerCase());
    }
    
    /**
     * Gets the complete URL for report validation by replacing placeholders
     * 
     * @param realmId the realm/company ID
     * @param reportId the report ID
     * @return the complete validation URL
     */
    public String getValidationUrl(String realmId, String reportId) {
        return baseUrl + reportValidationEndpoint
            .replace("{realmId}", realmId)
            .replace("{reportId}", reportId);
    }
}
