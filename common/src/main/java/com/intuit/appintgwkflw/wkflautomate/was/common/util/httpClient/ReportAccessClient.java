package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.QUICKBOOKS_API;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.constants.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import io.github.resilience4j.retry.annotation.Retry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * HTTP client for making report access validation API calls.
 * This client provides retry and circuit breaker capabilities for report validation requests.
 * 
 * <AUTHOR>
 */
@Retry(name = ResiliencyConstants.HTTP_CLIENT_RETRY)
@Component
public class ReportAccessClient {

    @Autowired
    private WASHttpClient client;

    /**
     * Makes HTTP request for report access validation with retry and circuit breaker capabilities.
     * 
     * @param wasHttpRequest the HTTP request containing validation details
     * @param <REQUEST> the request type
     * @param <RESPONSE> the response type
     * @return WASHttpResponse containing the validation result
     */
    @ServiceMetric(serviceName = QUICKBOOKS_API, type = HTTP)
    public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
        final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {
        return client.httpResponse(wasHttpRequest);
    }

    /**
     * Makes HTTP request for batch report access validation.
     * 
     * @param wasHttpRequest the HTTP request containing batch validation details
     * @param <REQUEST> the request type
     * @param <RESPONSE> the response type
     * @return WASHttpResponse containing the batch validation results
     */
    @ServiceMetric(serviceName = QUICKBOOKS_API, type = HTTP)
    public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> batchValidationRequest(
        final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {
        return client.httpResponse(wasHttpRequest);
    }
}
