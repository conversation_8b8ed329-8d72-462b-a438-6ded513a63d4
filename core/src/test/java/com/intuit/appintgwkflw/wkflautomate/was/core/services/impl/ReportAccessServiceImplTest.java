package com.intuit.appintgwkflw.wkflautomate.was.core.services.impl;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ReportAccessConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.ReportAccessClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.services.impl.ReportAccessServiceImpl.ReportValidationResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

/**
 * Unit tests for ReportAccessServiceImpl
 */
@RunWith(MockitoJUnitRunner.class)
public class ReportAccessServiceImplTest {

    @Mock
    private ReportAccessConfig reportAccessConfig;

    @Mock
    private ReportAccessClient reportAccessClient;

    @Mock
    private HeaderPopulator headerPopulator;

    @Mock
    private WASContextHandler contextHandler;

    @InjectMocks
    private ReportAccessServiceImpl reportAccessService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testValidateReportAccess_Success() {
        // Arrange
        String reportId = "12345";
        String realmId = "67890";
        String reportType = "invoice";

        when(reportAccessConfig.isEnabled()).thenReturn(true);
        when(reportAccessConfig.isSupportedReportType(reportType)).thenReturn(true);
        when(reportAccessConfig.getValidationUrl(realmId, reportId))
            .thenReturn("https://api.test.com/validate/67890/12345");

        ReportValidationResponse mockResponse = new ReportValidationResponse();
        mockResponse.setHasAccess(true);
        mockResponse.setReportId(reportId);
        mockResponse.setRealmId(realmId);

        WASHttpResponse<ReportValidationResponse> httpResponse = WASHttpResponse.<ReportValidationResponse>builder()
            .response(mockResponse)
            .status(HttpStatus.OK)
            .build();

        when(reportAccessClient.httpResponse(any())).thenReturn(httpResponse);

        // Act
        boolean result = reportAccessService.validateReportAccess(reportId, realmId, reportType);

        // Assert
        assertTrue("Should return true for valid report access", result);
    }

    @Test
    public void testValidateReportAccess_AccessDenied() {
        // Arrange
        String reportId = "12345";
        String realmId = "67890";
        String reportType = "invoice";

        when(reportAccessConfig.isEnabled()).thenReturn(true);
        when(reportAccessConfig.isSupportedReportType(reportType)).thenReturn(true);
        when(reportAccessConfig.getValidationUrl(realmId, reportId))
            .thenReturn("https://api.test.com/validate/67890/12345");

        ReportValidationResponse mockResponse = new ReportValidationResponse();
        mockResponse.setHasAccess(false);
        mockResponse.setReportId(reportId);
        mockResponse.setRealmId(realmId);

        WASHttpResponse<ReportValidationResponse> httpResponse = WASHttpResponse.<ReportValidationResponse>builder()
            .response(mockResponse)
            .status(HttpStatus.OK)
            .build();

        when(reportAccessClient.httpResponse(any())).thenReturn(httpResponse);

        // Act
        boolean result = reportAccessService.validateReportAccess(reportId, realmId, reportType);

        // Assert
        assertFalse("Should return false for denied report access", result);
    }

    @Test
    public void testValidateReportAccess_ValidationDisabled() {
        // Arrange
        String reportId = "12345";
        String realmId = "67890";
        String reportType = "invoice";

        when(reportAccessConfig.isEnabled()).thenReturn(false);

        // Act
        boolean result = reportAccessService.validateReportAccess(reportId, realmId, reportType);

        // Assert
        assertTrue("Should return true when validation is disabled", result);
    }

    @Test
    public void testValidateReportAccess_UnsupportedReportType() {
        // Arrange
        String reportId = "12345";
        String realmId = "67890";
        String reportType = "unsupported";

        when(reportAccessConfig.isEnabled()).thenReturn(true);
        when(reportAccessConfig.isSupportedReportType(reportType)).thenReturn(false);

        // Act
        boolean result = reportAccessService.validateReportAccess(reportId, realmId, reportType);

        // Assert
        assertTrue("Should return true for unsupported report types", result);
    }

    @Test
    public void testValidateReportAccess_ReportNotFound() {
        // Arrange
        String reportId = "12345";
        String realmId = "67890";
        String reportType = "invoice";

        when(reportAccessConfig.isEnabled()).thenReturn(true);
        when(reportAccessConfig.isSupportedReportType(reportType)).thenReturn(true);
        when(reportAccessConfig.getValidationUrl(realmId, reportId))
            .thenReturn("https://api.test.com/validate/67890/12345");

        WASHttpResponse<ReportValidationResponse> httpResponse = WASHttpResponse.<ReportValidationResponse>builder()
            .response(null)
            .status(HttpStatus.NOT_FOUND)
            .build();

        when(reportAccessClient.httpResponse(any())).thenReturn(httpResponse);

        // Act
        boolean result = reportAccessService.validateReportAccess(reportId, realmId, reportType);

        // Assert
        assertFalse("Should return false when report is not found", result);
    }

    @Test
    public void testValidateMultipleReportAccess_ValidationDisabled() {
        // Arrange
        List<String> reportIds = Arrays.asList("123", "456", "789");
        String realmId = "67890";
        String reportType = "invoice";

        when(reportAccessConfig.isEnabled()).thenReturn(false);

        // Act
        Map<String, Boolean> results = reportAccessService.validateMultipleReportAccess(reportIds, realmId, reportType);

        // Assert
        assertTrue("All reports should be allowed when validation is disabled", 
            results.values().stream().allMatch(Boolean::booleanValue));
    }

    @Test
    public void testIsValidationEnabled() {
        // Arrange
        when(reportAccessConfig.isEnabled()).thenReturn(true);

        // Act & Assert
        assertTrue("Should return config value", reportAccessService.isValidationEnabled());

        // Arrange
        when(reportAccessConfig.isEnabled()).thenReturn(false);

        // Act & Assert
        assertFalse("Should return config value", reportAccessService.isValidationEnabled());
    }

    @Test
    public void testIsSupportedReportType() {
        // Arrange
        String reportType = "invoice";
        when(reportAccessConfig.isSupportedReportType(reportType)).thenReturn(true);

        // Act & Assert
        assertTrue("Should return config value for supported type", 
            reportAccessService.isSupportedReportType(reportType));

        // Arrange
        String unsupportedType = "unsupported";
        when(reportAccessConfig.isSupportedReportType(unsupportedType)).thenReturn(false);

        // Act & Assert
        assertFalse("Should return config value for unsupported type", 
            reportAccessService.isSupportedReportType(unsupportedType));
    }
}
