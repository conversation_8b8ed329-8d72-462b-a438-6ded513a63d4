package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.services.ReportAccessService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Utility class for validating report IDs in workflow notification variables.
 * This class extracts report IDs from placeholder variables and validates access permissions.
 * 
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ReportIdValidator {

    private final ReportAccessService reportAccessService;

    // Regex patterns for different report ID formats
    private static final Pattern REPORT_ID_PATTERN = Pattern.compile("\\[\\[([^\\]]+)\\]\\]");
    private static final Pattern SPECIFIC_REPORT_PATTERN = Pattern.compile("\\[\\[report:([^\\]]+)\\]\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern TYPED_REPORT_PATTERN = Pattern.compile("\\[\\[([a-zA-Z]+):([^\\]]+)\\]\\]");
    
    // Common report-related variable names
    private static final List<String> REPORT_VARIABLE_NAMES = List.of(
        "reportid", "report_id", "report-id", "reportnumber", "report_number", "report-number",
        "invoiceid", "invoice_id", "invoice-id", "invoicenumber", "invoice_number", "invoice-number",
        "billid", "bill_id", "bill-id", "billnumber", "bill_number", "bill-number",
        "paymentid", "payment_id", "payment-id", "paymentnumber", "payment_number", "payment-number",
        "estimateid", "estimate_id", "estimate-id", "estimatenumber", "estimate_number", "estimate-number"
    );

    /**
     * Validates all report IDs found in the input variables map.
     * 
     * @param inputVariables map of variable names to values
     * @param realmId the realm/company ID of the requesting user
     * @throws WorkflowGeneralException if any report ID validation fails
     */
    public void validateReportIdsInVariables(Map<String, String> inputVariables, String realmId) {
        if (CollectionUtils.isEmpty(inputVariables) || StringUtils.isBlank(realmId)) {
            return;
        }

        // Skip validation if service is disabled
        if (!reportAccessService.isValidationEnabled()) {
            WorkflowLogger.logInfo("Report ID validation is disabled, skipping validation");
            return;
        }

        List<ReportIdInfo> extractedReportIds = extractReportIds(inputVariables);
        
        if (extractedReportIds.isEmpty()) {
            WorkflowLogger.logInfo("No report IDs found in variables, skipping validation");
            return;
        }

        WorkflowLogger.logInfo("Found %d report IDs to validate for realm %s", extractedReportIds.size(), realmId);

        // Group report IDs by type for efficient batch validation
        Map<String, List<String>> reportIdsByType = groupReportIdsByType(extractedReportIds);

        // Validate each group
        for (Map.Entry<String, List<String>> entry : reportIdsByType.entrySet()) {
            String reportType = entry.getKey();
            List<String> reportIds = entry.getValue();

            Map<String, Boolean> validationResults = reportAccessService.validateMultipleReportAccess(
                reportIds, realmId, reportType);

            // Check for any failed validations
            for (Map.Entry<String, Boolean> result : validationResults.entrySet()) {
                String reportId = result.getKey();
                Boolean hasAccess = result.getValue();

                if (!Boolean.TRUE.equals(hasAccess)) {
                    WorkflowLogger.logError("Report access validation failed: reportId=%s, realmId=%s, reportType=%s", 
                        reportId, realmId, reportType);
                    throw new WorkflowGeneralException(
                        WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS,
                        String.format("Access denied for report ID: %s. Report does not belong to your organization.", reportId)
                    );
                }
            }
        }

        WorkflowLogger.logInfo("All report ID validations passed for realm %s", realmId);
    }

    /**
     * Extracts report IDs from input variables using various patterns.
     * 
     * @param inputVariables map of variable names to values
     * @return list of extracted report ID information
     */
    public List<ReportIdInfo> extractReportIds(Map<String, String> inputVariables) {
        List<ReportIdInfo> reportIds = new ArrayList<>();

        for (Map.Entry<String, String> entry : inputVariables.entrySet()) {
            String variableName = entry.getKey();
            String variableValue = entry.getValue();

            if (StringUtils.isBlank(variableValue)) {
                continue;
            }

            // Extract from placeholder patterns
            reportIds.addAll(extractFromPlaceholders(variableValue));

            // Extract from variable names that suggest report IDs
            reportIds.addAll(extractFromVariableNames(variableName, variableValue));
        }

        return reportIds;
    }

    /**
     * Extracts report IDs from placeholder patterns like [[report:123]] or [[invoice:456]].
     */
    private List<ReportIdInfo> extractFromPlaceholders(String value) {
        List<ReportIdInfo> reportIds = new ArrayList<>();

        // Pattern 1: [[report:ID]]
        Matcher specificMatcher = SPECIFIC_REPORT_PATTERN.matcher(value);
        while (specificMatcher.find()) {
            String reportId = specificMatcher.group(1);
            reportIds.add(new ReportIdInfo(reportId, "report", "placeholder"));
        }

        // Pattern 2: [[type:ID]] for various types
        Matcher typedMatcher = TYPED_REPORT_PATTERN.matcher(value);
        while (typedMatcher.find()) {
            String type = typedMatcher.group(1).toLowerCase();
            String reportId = typedMatcher.group(2);

            if (reportAccessService.isSupportedReportType(type)) {
                reportIds.add(new ReportIdInfo(reportId, type, "typed_placeholder"));
            }
        }

        return reportIds;
    }

    /**
     * Extracts report IDs from variable names that suggest they contain report IDs.
     */
    private List<ReportIdInfo> extractFromVariableNames(String variableName, String variableValue) {
        List<ReportIdInfo> reportIds = new ArrayList<>();

        String lowerVariableName = variableName.toLowerCase();

        for (String reportVariableName : REPORT_VARIABLE_NAMES) {
            if (lowerVariableName.contains(reportVariableName)) {
                // Determine report type from variable name
                String reportType = determineReportTypeFromVariableName(lowerVariableName);
                
                // Extract potential report IDs from the value
                List<String> extractedIds = extractPotentialReportIds(variableValue);
                
                for (String reportId : extractedIds) {
                    reportIds.add(new ReportIdInfo(reportId, reportType, "variable_name"));
                }
                break;
            }
        }

        return reportIds;
    }

    /**
     * Determines report type from variable name.
     */
    private String determineReportTypeFromVariableName(String variableName) {
        if (variableName.contains("invoice")) return "invoice";
        if (variableName.contains("bill")) return "bill";
        if (variableName.contains("payment")) return "payment";
        if (variableName.contains("estimate")) return "estimate";
        if (variableName.contains("customer")) return "customer";
        if (variableName.contains("vendor")) return "vendor";
        if (variableName.contains("item")) return "item";
        if (variableName.contains("account")) return "account";
        return "report"; // default type
    }

    /**
     * Extracts potential report IDs from a value string.
     */
    private List<String> extractPotentialReportIds(String value) {
        List<String> reportIds = new ArrayList<>();

        // Look for numeric IDs
        Pattern numericPattern = Pattern.compile("\\b\\d+\\b");
        Matcher matcher = numericPattern.matcher(value);
        while (matcher.find()) {
            String potentialId = matcher.group();
            // Only consider IDs with reasonable length (avoid single digits, very long numbers)
            if (potentialId.length() >= 2 && potentialId.length() <= 20) {
                reportIds.add(potentialId);
            }
        }

        // Look for alphanumeric IDs
        Pattern alphanumericPattern = Pattern.compile("\\b[A-Za-z0-9]{8,}\\b");
        matcher = alphanumericPattern.matcher(value);
        while (matcher.find()) {
            reportIds.add(matcher.group());
        }

        return reportIds;
    }

    /**
     * Groups report IDs by their type for efficient batch validation.
     */
    private Map<String, List<String>> groupReportIdsByType(List<ReportIdInfo> reportIds) {
        Map<String, List<String>> grouped = new HashMap<>();

        for (ReportIdInfo reportIdInfo : reportIds) {
            String type = reportIdInfo.getReportType();
            grouped.computeIfAbsent(type, k -> new ArrayList<>()).add(reportIdInfo.getReportId());
        }

        return grouped;
    }

    /**
     * Information about an extracted report ID.
     */
    @Getter
    @Setter
    public static class ReportIdInfo {
        private String reportId;
        private String reportType;
        private String extractionMethod;

        public ReportIdInfo(String reportId, String reportType, String extractionMethod) {
            this.reportId = reportId;
            this.reportType = reportType;
            this.extractionMethod = extractionMethod;
        }
    }
}
