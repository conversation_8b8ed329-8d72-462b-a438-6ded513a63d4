package com.intuit.appintgwkflw.wkflautomate.was.core.services.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ReportAccessConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.ReportAccessClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.services.ReportAccessService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Implementation of ReportAccessService for validating report access across realms.
 * This service makes API calls to verify report ownership and access permissions.
 * 
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class ReportAccessServiceImpl implements ReportAccessService {

    private final ReportAccessConfig reportAccessConfig;
    private final ReportAccessClient reportAccessClient;
    private final HeaderPopulator headerPopulator;
    private final WASContextHandler contextHandler;

    @Override
    public boolean validateReportAccess(String reportId, String realmId, String reportType) {
        // Skip validation if disabled
        if (!isValidationEnabled()) {
            WorkflowLogger.logInfo("Report access validation is disabled, allowing access for reportId=%s", reportId);
            return true;
        }

        // Validate input parameters
        if (StringUtils.isBlank(reportId) || StringUtils.isBlank(realmId)) {
            WorkflowLogger.logError("Invalid input parameters: reportId=%s, realmId=%s", reportId, realmId);
            return false;
        }

        // Check if report type is supported
        if (StringUtils.isNotBlank(reportType) && !isSupportedReportType(reportType)) {
            WorkflowLogger.logInfo("Report type %s is not supported for validation, allowing access", reportType);
            return true;
        }

        try {
            // Build validation request
            String validationUrl = reportAccessConfig.getValidationUrl(realmId, reportId);
            
            WASHttpRequest<Void, ReportValidationResponse> request = WASHttpRequest.<Void, ReportValidationResponse>builder()
                .url(validationUrl)
                .httpMethod(HttpMethod.GET)
                .requestHeaders(headerPopulator.populateHeaders())
                .responseType(new ParameterizedTypeReference<ReportValidationResponse>() {})
                .build();

            // Make API call
            WASHttpResponse<ReportValidationResponse> response = reportAccessClient.httpResponse(request);

            // Process response
            if (response.getStatus() == HttpStatus.OK && response.getResponse() != null) {
                boolean hasAccess = response.getResponse().isHasAccess();
                WorkflowLogger.logInfo("Report access validation result: reportId=%s, realmId=%s, hasAccess=%s", 
                    reportId, realmId, hasAccess);
                return hasAccess;
            } else if (response.getStatus() == HttpStatus.NOT_FOUND) {
                WorkflowLogger.logInfo("Report not found: reportId=%s, realmId=%s", reportId, realmId);
                return false;
            } else {
                WorkflowLogger.logError("Report validation API returned unexpected status: %s for reportId=%s", 
                    response.getStatus(), reportId);
                return false;
            }

        } catch (Exception e) {
            WorkflowLogger.logError("Error validating report access for reportId=%s, realmId=%s: %s", 
                reportId, realmId, e.getMessage());
            // In case of API failure, deny access for security
            return false;
        }
    }

    @Override
    public Map<String, Boolean> validateMultipleReportAccess(List<String> reportIds, String realmId, String reportType) {
        Map<String, Boolean> results = new HashMap<>();

        if (CollectionUtils.isEmpty(reportIds)) {
            return results;
        }

        // If batch validation is not enabled or list is too large, validate individually
        if (!reportAccessConfig.isBatchValidationEnabled() || reportIds.size() > reportAccessConfig.getMaxBatchSize()) {
            return reportIds.stream()
                .collect(Collectors.toMap(
                    reportId -> reportId,
                    reportId -> validateReportAccess(reportId, realmId, reportType)
                ));
        }

        // Skip validation if disabled
        if (!isValidationEnabled()) {
            return reportIds.stream()
                .collect(Collectors.toMap(reportId -> reportId, reportId -> true));
        }

        try {
            // Build batch validation request
            BatchReportValidationRequest batchRequest = new BatchReportValidationRequest();
            batchRequest.setReportIds(reportIds);
            batchRequest.setRealmId(realmId);
            batchRequest.setReportType(reportType);

            String batchValidationUrl = reportAccessConfig.getBaseUrl() + "/v3/companyinfo/" + realmId + "/reports/batch-validate";

            WASHttpRequest<BatchReportValidationRequest, BatchReportValidationResponse> request = 
                WASHttpRequest.<BatchReportValidationRequest, BatchReportValidationResponse>builder()
                    .url(batchValidationUrl)
                    .httpMethod(HttpMethod.POST)
                    .request(batchRequest)
                    .requestHeaders(headerPopulator.populateHeaders())
                    .responseType(new ParameterizedTypeReference<BatchReportValidationResponse>() {})
                    .build();

            // Make batch API call
            WASHttpResponse<BatchReportValidationResponse> response = reportAccessClient.batchValidationRequest(request);

            if (response.getStatus() == HttpStatus.OK && response.getResponse() != null) {
                results = response.getResponse().getValidationResults();
                WorkflowLogger.logInfo("Batch report validation completed for %d reports in realm %s", 
                    reportIds.size(), realmId);
            } else {
                WorkflowLogger.logError("Batch validation failed, falling back to individual validation");
                // Fallback to individual validation
                results = reportIds.stream()
                    .collect(Collectors.toMap(
                        reportId -> reportId,
                        reportId -> validateReportAccess(reportId, realmId, reportType)
                    ));
            }

        } catch (Exception e) {
            WorkflowLogger.logError("Error in batch report validation: %s", e.getMessage());
            // Fallback to individual validation
            results = reportIds.stream()
                .collect(Collectors.toMap(
                    reportId -> reportId,
                    reportId -> validateReportAccess(reportId, realmId, reportType)
                ));
        }

        return results;
    }

    @Override
    public Map<String, Boolean> validateMixedReportAccess(Map<String, String> reportValidationRequests, String realmId) {
        Map<String, Boolean> results = new HashMap<>();

        if (CollectionUtils.isEmpty(reportValidationRequests)) {
            return results;
        }

        // Validate each report individually with its specific type
        for (Map.Entry<String, String> entry : reportValidationRequests.entrySet()) {
            String reportId = entry.getKey();
            String reportType = entry.getValue();
            boolean hasAccess = validateReportAccess(reportId, realmId, reportType);
            results.put(reportId, hasAccess);
        }

        return results;
    }

    @Override
    public boolean isValidationEnabled() {
        return reportAccessConfig.isEnabled();
    }

    @Override
    public boolean isSupportedReportType(String reportType) {
        return reportAccessConfig.isSupportedReportType(reportType);
    }

    /**
     * Response model for single report validation
     */
    public static class ReportValidationResponse {
        private boolean hasAccess;
        private String reportId;
        private String realmId;
        private String message;

        // Getters and setters
        public boolean isHasAccess() { return hasAccess; }
        public void setHasAccess(boolean hasAccess) { this.hasAccess = hasAccess; }
        public String getReportId() { return reportId; }
        public void setReportId(String reportId) { this.reportId = reportId; }
        public String getRealmId() { return realmId; }
        public void setRealmId(String realmId) { this.realmId = realmId; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * Request model for batch report validation
     */
    public static class BatchReportValidationRequest {
        private List<String> reportIds;
        private String realmId;
        private String reportType;

        // Getters and setters
        public List<String> getReportIds() { return reportIds; }
        public void setReportIds(List<String> reportIds) { this.reportIds = reportIds; }
        public String getRealmId() { return realmId; }
        public void setRealmId(String realmId) { this.realmId = realmId; }
        public String getReportType() { return reportType; }
        public void setReportType(String reportType) { this.reportType = reportType; }
    }

    /**
     * Response model for batch report validation
     */
    public static class BatchReportValidationResponse {
        private Map<String, Boolean> validationResults;
        private String message;

        // Getters and setters
        public Map<String, Boolean> getValidationResults() { return validationResults; }
        public void setValidationResults(Map<String, Boolean> validationResults) { this.validationResults = validationResults; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
