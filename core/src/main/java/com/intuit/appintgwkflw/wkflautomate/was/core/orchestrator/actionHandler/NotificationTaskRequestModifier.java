package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ReportIdValidator;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SubstitutePlaceholderUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;
/**
 * Modifies the WorkflowTask request of NOTIFICATION_TASK
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class NotificationTaskRequestModifier implements TaskRequestModifier {
  private ParameterDetailsExtractorHelper parameterDetailsExtractorHelper;
  private ProcessDetailsRepoService processDetailsRepoService;
  private AuthHelper authHelper;
  private ReportIdValidator reportIdValidator;

  public TaskType getName() {
    return TaskType.NOTIFICATION_TASK;
  }
  /**
   * fills all the processVariables and ParameterVariables in a single map
   *
   * @param parameterDetails
   * @param inputVariables
   * @return inputMap
   */
  private Map<String, String> getInputMap(
      Map<String, HandlerDetails.ParameterDetails> parameterDetails,
      Map<String, String> inputVariables) {
    Map<String, String> inputMap = new HashMap<>();
    inputMap.putAll(inputVariables);
    parameterDetails.forEach(
        (key, value) -> {
          if (!ObjectUtils.isEmpty(value.getFieldValue())) {
            inputMap.put(key, value.getFieldValue().get(0));
          }
        });
    return inputMap;
  }
  /**
   * Populates the notificationDetails from the inputMap
   *
   * @param taskRequest
   * @param bridgeInputMap
   */
  private void populateNotificationDetails(
      WorkflowTaskRequest taskRequest, Map<String, String> bridgeInputMap) {
    if (bridgeInputMap.containsKey(OinpBridgeConstants.NOTIFICATION_DATA)) {
      taskRequest
          .getTaskAttributes()
          .getVariables()
          .put(
              OinpBridgeConstants.NOTIFICATION_DATA,
              bridgeInputMap.get(OinpBridgeConstants.NOTIFICATION_DATA));
      taskRequest
          .getTaskAttributes()
          .getRuntimeAttributes()
          .put(
              OinpBridgeConstants.NOTIFICATION_DATA,
              bridgeInputMap.get(OinpBridgeConstants.NOTIFICATION_DATA));
      if (bridgeInputMap.containsKey(OinpBridgeConstants.NOTIFICATION_METADATA)) {
        taskRequest
            .getTaskAttributes()
            .getVariables()
            .put(
                OinpBridgeConstants.NOTIFICATION_METADATA,
                bridgeInputMap.get(OinpBridgeConstants.NOTIFICATION_METADATA));
        taskRequest
            .getTaskAttributes()
            .getRuntimeAttributes()
            .put(
                OinpBridgeConstants.NOTIFICATION_METADATA,
                bridgeInputMap.get(OinpBridgeConstants.NOTIFICATION_METADATA));
      }
      if (bridgeInputMap.containsKey(OinpBridgeConstants.NOTIFICATION_NAME)) {
        taskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .put(
                OinpBridgeConstants.NOTIFICATION_NAME,
                bridgeInputMap.get(OinpBridgeConstants.NOTIFICATION_NAME));
      }
      if (bridgeInputMap.containsKey(OinpBridgeConstants.NOTIFICATION_DATA_TYPE)) {
        taskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .put(
                OinpBridgeConstants.NOTIFICATION_DATA_TYPE,
                bridgeInputMap.get(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));
      }
      if (bridgeInputMap.containsKey(OinpBridgeConstants.SERVICE_NAME)) {
        taskRequest
            .getTaskAttributes()
            .getModelAttributes()
            .put(
                OinpBridgeConstants.SERVICE_NAME,
                bridgeInputMap.get(OinpBridgeConstants.SERVICE_NAME));
      }
    }
  }
  /**
   * Modifies the taskRequest with all the notificationDetails required
   *
   * @param taskRequest
   * @param workerActionRequest
   * @return taskRequest
   */
  @Override
  public WorkflowTaskRequest getTaskRequest(
      WorkflowTaskRequest taskRequest, WorkerActionRequest workerActionRequest) {
    Optional<DefinitionDetails> definitionDetails =
        processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            workerActionRequest.fetchParentProcessInstanceId());

    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
        parameterDetailsExtractorHelper.getParameterDetails(
            workerActionRequest, definitionDetails.get());
    if (parameterDetailsMap.isPresent()) {
      WorkflowVerfiy.verify(
          CollectionUtils.isEmpty(parameterDetailsMap.get()), INVALID_PARAMETER_DETAILS);
      Map<String, String> inputMap =
          getInputMap(parameterDetailsMap.get(), workerActionRequest.getInputVariables());

      // locale will be part of notificationMetadata and retrieved from definition placeholderValue
      inputMap.put(WorkflowConstants.LOCALE, getLocaleMetadata(definitionDetails.get()));

      // Get realm ID for report validation
      String realmId = String.valueOf(workerActionRequest.getOwnerId());

      // replaces all the placeholder values present in the bridgeInputMap with report access validation
      Map<String, String> substitutedInputMap =
          SubstitutePlaceholderUtil.substitutePlaceholderWithValidation(inputMap, false, realmId, reportIdValidator);
      populateNotificationDetails(taskRequest, substitutedInputMap);
    }
    return taskRequest;
  }

  private String getLocaleMetadata(DefinitionDetails definitionDetails) {
    String locale = SingleDefinitionUtil.getLocaleFromPlaceHolderValues(definitionDetails);
    // string manipulation done to enable conversion from string to java.util.Locale
    return Objects.nonNull(locale)
        ? locale
        : StringUtils.EMPTY;
  }
}
