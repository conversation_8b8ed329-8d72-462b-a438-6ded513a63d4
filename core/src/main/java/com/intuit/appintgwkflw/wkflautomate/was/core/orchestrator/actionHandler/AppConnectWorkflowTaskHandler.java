package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_PARAMETER_DETAILS;
import static java.lang.Boolean.TRUE;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectTaskHandlerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.TaskDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.adapter.AppConnectOINPBridge;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class AppConnectWorkflowTaskHandler extends WorkflowTaskHandler {

  private final WorkerUtil workerUtil;
  private final AppConnectWorkflowTaskHandlerHelper taskHandlerHelper;
  private final ProcessDetailsRepoService processDetailsRepoService;
  private final AppConnectOINPBridge appConnectOINPBridge;
  private final ParameterDetailsExtractorHelper parameterDetailsExtractorHelper;

  @Override
  public TaskHandlerName getName() {

    return TaskHandlerName.APP_CONNECT_WORKFLOW_TASK_ACTION_HANDLER;
  }
  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {

    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    Optional<DefinitionDetails> definitionDetails =
        processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            workerActionRequest.getProcessInstanceId());
    WorkflowVerfiy.verify(!definitionDetails.isPresent(), WorkflowError.DEFINITION_NOT_FOUND);

    /* Throw exception if definition is marked for disable/delete/error */
    workerActionRequest = workerUtil.validate(workerActionRequest);

    Optional<Map<String, ParameterDetails>> parameterDetailsMap =
        parameterDetailsExtractorHelper.getParameterDetails(workerActionRequest, definitionDetails.get());
    WorkflowVerfiy.verify(
        !parameterDetailsMap.isPresent() || CollectionUtils.isEmpty(parameterDetailsMap.get()),
        INVALID_PARAMETER_DETAILS);
    AppConnectTaskHandlerRequest actionRequest =
        taskHandlerHelper.prepareTaskHandlerRequest(
            workerActionRequest, parameterDetailsMap, definitionDetails.get());

    Map<String, String> bridgeInputMap = Optional.ofNullable(actionRequest.getAction())
            .map(action -> Optional.ofNullable(action.getInputs())
                    .map( inputs -> inputs.stream().collect(
                            Collectors.toMap(WorkflowTaskHandlerInput::getName, WorkflowTaskHandlerInput::getValue)))
                    .orElse(Collections.emptyMap()))
            .orElse(Collections.emptyMap());

    /*
     * Calling the OINP bridge adapter based on a feature flag.
     */
    if (appConnectOINPBridge.initiateBridge(workerActionRequest, bridgeInputMap)) {
      try {
        return appConnectOINPBridge.executeNotificationAction(workerActionRequest, bridgeInputMap);
      }
      catch (Exception e){
        WorkflowLogger.logError(e, "OINP Bridge Failed externalTaskId=%s", workerActionRequest.getTaskId());
        setFatal(workerActionRequest);
        throw e;
      }
    }

    List<WorkflowTaskHandlerInput> additionalTaskHandlerInputs = new ArrayList<>();
    /**
     * For some handler we have hardcoded the parameters map which can't be changed without bpmn changes
     * so here we are fetching those require fields from config and injecting it in input request
     */
    Optional<RecordType> recordTypeOptional = extractRecordType(definitionDetails.get(), workerActionRequest);
    if (recordTypeOptional.isPresent()) {
      additionalTaskHandlerInputs = getTaskParametersFromConfig(recordTypeOptional.get(), workerActionRequest);
    }

    if (!CollectionUtils.isEmpty(additionalTaskHandlerInputs)) {

      List<WorkflowTaskHandlerInput> finalHandlerInputs = additionalTaskHandlerInputs;
      Optional.of(actionRequest).map(AppConnectTaskHandlerRequest::getAction).map(WorkflowTaskHandlerAction::getInputs).ifPresent(inputs -> {
        Set<String> existingInputNames = inputs.stream().map(WorkflowTaskHandlerInput::getName).collect(Collectors.toSet());

        finalHandlerInputs.stream().filter(input -> {
          boolean isNotPresent = !existingInputNames.contains(input.getName());
          if (isNotPresent) {
            WorkflowLogger.logInfo("Adding new input: %s with value: %s", input.getName(), input.getValue());
          }
          return isNotPresent;
        }).forEach(inputs::add);
      });

    }

    WASHttpRequest<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<AppConnectTaskHandlerRequest, WorkflowTaskHandlerResponse>builder()
            .url(actionRequest.getEndpoint())
            .request(actionRequest)
            .httpMethod(HttpMethod.POST)
            .retryHandler(RetryHandlerName.APPCONNECT_DOWNSTREAM_ERRORS)
            .responseType(new ParameterizedTypeReference<WorkflowTaskHandlerResponse>() {})
            .build();

    // execute app connect workflow realm based action
    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        taskHandlerHelper.executeAction(wasHttpRequest, workerActionRequest.getHandlerId());

    // if error occurred  in executing external task throw exception
    WorkflowVerfiy.verify(
        (!response.isSuccess2xx()
            || null == response.getResponse()
            || isEmpty(response.getResponse().getSuccess())
            || !TRUE.toString().equals(response.getResponse().getSuccess())),
            taskHandlerHelper.getAppConnectDuzzitException(workerActionRequest.getHandlerId(), response),
        StringUtils.defaultIfBlank(
            response.getError(), getErrorFromResponse(response.getResponse())));

    return taskHandlerHelper.prepareTaskHandlerResponse(workerActionRequest, response);
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.INVOKE_DUZZIT, Type.APP_CONNECT_METRIC, exception);
  }

  private String getErrorFromResponse(final WorkflowTaskHandlerResponse taskHandlerResponse) {

    return nonNull(taskHandlerResponse) ? taskHandlerResponse.getError() : null;
  }

  /**
   * Set fatal as true in case appconnect send notification bridge fails.
   * This is done to maintain resiliency.
   * If fatal is true, the externalTask worker will set default retry for the task.
   * Default retry is 3 times at interval of 15 seconds.
   * In case {@link TaskDetails#getRetryStrategyName()}(exponential/linear backoff) is defined, it will be invoked.
   * @param workerActionRequest contains details.
   */
  private void setFatal(WorkerActionRequest workerActionRequest){
    final Optional<HandlerDetails.TaskDetails> taskDetailsOptional =
        SchemaDecoder.getTaskDetails(workerActionRequest.getInputVariables());
    taskDetailsOptional.ifPresent(taskDetails -> {
      taskDetails.setFatal(true);
      workerActionRequest.getInputVariables()
          .put(WorkFlowVariables.TASK_DETAILS_KEY.getName(), ObjectConverter.toJson(taskDetails));
    });
  }

}
