package com.intuit.appintgwkflw.wkflautomate.was.core.util;


import java.util.Collections;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.text.StrSubstitutor;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * Replace the placeholders in the input using key/value in map
 * Enhanced with report access validation to prevent cross-realm security vulnerabilities
 */
@UtilityClass
public class SubstitutePlaceholderUtil {

  private static final String PLACEHOLDER_SUFFIX = "]]";
  private static final String PLACEHOLDER_PREFIX = "[[";

  /**
   * Returns input map after substitution of placeholders
   * @param inputMap contains key/value which have to be evaluated and replaced
   * @param isNestedSubstitutionEnabled true-> will substitute the values recursively
   *                             false-> will simply substitute the values
   * @return map of substituted values
   */
  public Map<String, String> substitutePlaceholder(Map<String, String> inputMap, boolean isNestedSubstitutionEnabled){
    if (CollectionUtils.isEmpty(inputMap)){
      return Collections.emptyMap();
    }
    StrSubstitutor strSubstitutor = new StrSubstitutor(inputMap, PLACEHOLDER_PREFIX, PLACEHOLDER_SUFFIX);
    strSubstitutor.setEnableSubstitutionInVariables(isNestedSubstitutionEnabled);
    return inputMap.entrySet().stream()
        .collect(Collectors.toMap(Entry::getKey,
            entry -> strSubstitutor.replace(entry.getValue())));
  }

  /**
   * Returns input map after substitution of placeholders with report access validation.
   * This method validates that any report IDs in the placeholders belong to the specified realm.
   *
   * @param inputMap contains key/value which have to be evaluated and replaced
   * @param isNestedSubstitutionEnabled true-> will substitute the values recursively
   * @param realmId the realm/company ID to validate report access against
   * @param reportIdValidator validator for checking report access permissions
   * @return map of substituted values
   * @throws com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException if report access validation fails
   */
  public Map<String, String> substitutePlaceholderWithValidation(
      Map<String, String> inputMap,
      boolean isNestedSubstitutionEnabled,
      String realmId,
      ReportIdValidator reportIdValidator) {

    if (CollectionUtils.isEmpty(inputMap)){
      return Collections.emptyMap();
    }

    // Validate report IDs before substitution
    if (reportIdValidator != null && realmId != null) {
      reportIdValidator.validateReportIdsInVariables(inputMap, realmId);
    }

    // Proceed with normal placeholder substitution
    return substitutePlaceholder(inputMap, isNestedSubstitutionEnabled);
  }
}
