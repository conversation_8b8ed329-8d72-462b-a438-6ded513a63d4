package com.intuit.appintgwkflw.wkflautomate.was.core.services;

import java.util.List;
import java.util.Map;

/**
 * Service interface for validating report access across different realms/companies.
 * This service ensures that users can only access reports that belong to their organization.
 * 
 * <AUTHOR>
 */
public interface ReportAccessService {

    /**
     * Validates if a user has access to a specific report.
     * Makes an API call to verify that the report belongs to the user's realm/company.
     * 
     * @param reportId the ID of the report to validate
     * @param realmId the realm/company ID of the requesting user
     * @param reportType the type of report (e.g., "invoice", "bill", "payment")
     * @return true if the user has access to the report, false otherwise
     */
    boolean validateReportAccess(String reportId, String realmId, String reportType);

    /**
     * Validates access to multiple reports in a single operation.
     * This method is more efficient for validating multiple reports at once.
     * 
     * @param reportIds list of report IDs to validate
     * @param realmId the realm/company ID of the requesting user
     * @param reportType the type of reports being validated
     * @return Map where key is reportId and value is boolean indicating access permission
     */
    Map<String, Boolean> validateMultipleReportAccess(List<String> reportIds, String realmId, String reportType);

    /**
     * Validates access to reports of different types in a single operation.
     * 
     * @param reportValidationRequests map where key is reportId and value is reportType
     * @param realmId the realm/company ID of the requesting user
     * @return Map where key is reportId and value is boolean indicating access permission
     */
    Map<String, Boolean> validateMixedReportAccess(Map<String, String> reportValidationRequests, String realmId);

    /**
     * Checks if report access validation is enabled in the configuration.
     * 
     * @return true if validation is enabled, false otherwise
     */
    boolean isValidationEnabled();

    /**
     * Checks if the given report type is supported for validation.
     * 
     * @param reportType the report type to check
     * @return true if the report type is supported, false otherwise
     */
    boolean isSupportedReportType(String reportType);
}
