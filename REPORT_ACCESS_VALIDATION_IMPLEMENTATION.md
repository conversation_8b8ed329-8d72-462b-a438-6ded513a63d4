# Report Access Validation Implementation

## Overview
This implementation provides a comprehensive solution to prevent cross-realm report access vulnerabilities in the workflow automation system. It validates that report IDs used in workflow notifications belong to the same realm/company as the requesting user.

## Security Vulnerability Fixed
**Problem**: Users could bypass organizational boundaries by specifying report IDs from different companies in workflow notifications, allowing unauthorized access to reports across realms.

**Solution**: Implemented report access validation that makes API calls to verify report ownership before allowing workflow creation or notification processing.

## Architecture

### Core Components

1. **ReportAccessConfig** - Configuration class for validation settings
2. **ReportAccessService** - Service interface for report validation
3. **ReportAccessServiceImpl** - Implementation with API call logic
4. **ReportAccessClient** - HTTP client with retry capabilities
5. **ReportIdValidator** - Utility for extracting and validating report IDs
6. **Enhanced SubstitutePlaceholderUtil** - Integrated validation during placeholder substitution

### Integration Points

1. **NotificationTaskRequestModifier** - Primary integration point for notification processing
2. **SubstitutePlaceholderUtil** - Enhanced with validation capabilities
3. **Configuration Files** - Environment-specific settings

## Implementation Details

### 1. Report ID Extraction Patterns

The system detects report IDs using multiple patterns:

- **Placeholder patterns**: `[[report:123]]`, `[[invoice:456]]`
- **Variable names**: `reportId`, `invoiceNumber`, `billId`, etc.
- **Numeric IDs**: Extracts potential report IDs from values

### 2. Validation Process

```java
// Example validation flow
1. Extract report IDs from notification variables
2. Group by report type for efficient batch processing
3. Make API calls to validate each report belongs to user's realm
4. Throw security exception if any validation fails
5. Continue with normal processing if all validations pass
```

### 3. API Integration

The service makes HTTP calls to QuickBooks API endpoints:
- **Single validation**: `/v3/companyinfo/{realmId}/reports/{reportId}/validate`
- **Batch validation**: `/v3/companyinfo/{realmId}/reports/batch-validate`

### 4. Error Handling

- **API failures**: Deny access for security (fail-safe approach)
- **Network timeouts**: Configurable retry with exponential backoff
- **Invalid responses**: Log and deny access

## Configuration

### Application Configuration

```yaml
report-access-validation:
  enabled: true
  baseUrl: https://quickbooks.api.intuit.com
  reportValidationEndpoint: /v3/companyinfo/{realmId}/reports/{reportId}/validate
  timeoutMs: 5000
  maxRetries: 3
  supportedReportTypes:
    - invoice
    - bill
    - payment
    - estimate
  batchValidationEnabled: true
  maxBatchSize: 50
  cachingEnabled: true
  cacheTtlSeconds: 300
```

### Environment-Specific Settings

- **Local**: Validation disabled for development
- **QAL/E2E**: Enabled with test endpoints
- **Production**: Enabled with conservative timeout settings

## Usage Examples

### 1. Basic Report Validation

```java
@Autowired
private ReportAccessService reportAccessService;

// Validate single report
boolean hasAccess = reportAccessService.validateReportAccess("12345", "67890", "invoice");

// Validate multiple reports
List<String> reportIds = Arrays.asList("123", "456", "789");
Map<String, Boolean> results = reportAccessService.validateMultipleReportAccess(
    reportIds, "67890", "invoice");
```

### 2. Enhanced Placeholder Substitution

```java
// With validation
Map<String, String> result = SubstitutePlaceholderUtil.substitutePlaceholderWithValidation(
    inputMap, false, realmId, reportIdValidator);

// Traditional (without validation)
Map<String, String> result = SubstitutePlaceholderUtil.substitutePlaceholder(
    inputMap, false);
```

### 3. Report ID Extraction

```java
@Autowired
private ReportIdValidator reportIdValidator;

// Extract and validate report IDs from variables
reportIdValidator.validateReportIdsInVariables(inputVariables, realmId);

// Just extract without validation
List<ReportIdInfo> reportIds = reportIdValidator.extractReportIds(inputVariables);
```

## Testing

### Unit Tests
- **ReportAccessServiceImplTest**: Tests service logic and API integration
- **ReportIdValidatorTest**: Tests report ID extraction patterns
- **SubstitutePlaceholderUtilTest**: Tests enhanced substitution with validation

### Integration Tests
- **End-to-end workflow creation with report IDs**
- **Cross-realm access attempt scenarios**
- **API failure handling**

## Performance Considerations

### 1. Batch Processing
- Groups report IDs by type for efficient validation
- Configurable batch size limits
- Fallback to individual validation if batch fails

### 2. Caching
- Configurable TTL for validation results
- Reduces API calls for frequently accessed reports
- Cache invalidation on realm changes

### 3. Async Processing
- Non-blocking validation for large batches
- Timeout handling to prevent workflow delays
- Circuit breaker pattern for API resilience

## Security Features

### 1. Fail-Safe Design
- Denies access on API failures or timeouts
- Validates all extracted report IDs before processing
- Logs security violations for monitoring

### 2. Input Validation
- Sanitizes report IDs and realm IDs
- Validates report types against supported list
- Prevents injection attacks through input validation

### 3. Audit Logging
- Logs all validation attempts and results
- Tracks cross-realm access attempts
- Provides security monitoring capabilities

## Deployment Checklist

### 1. Configuration
- [ ] Update application.yaml with appropriate endpoints
- [ ] Configure environment-specific settings
- [ ] Set up monitoring and alerting

### 2. API Integration
- [ ] Verify QuickBooks API endpoints are accessible
- [ ] Test authentication and authorization
- [ ] Validate response formats

### 3. Testing
- [ ] Run unit tests
- [ ] Execute integration tests
- [ ] Perform security testing with cross-realm scenarios

### 4. Monitoring
- [ ] Set up metrics for validation success/failure rates
- [ ] Configure alerts for security violations
- [ ] Monitor API response times and error rates

## Rollback Plan

If issues arise, the validation can be disabled by:
1. Setting `report-access-validation.enabled: false` in configuration
2. Restarting the service
3. The system will fall back to original behavior without validation

## Future Enhancements

1. **Machine Learning**: Detect suspicious report access patterns
2. **Real-time Monitoring**: Dashboard for security violations
3. **Advanced Caching**: Redis-based distributed cache
4. **Webhook Integration**: Real-time report ownership updates
5. **GraphQL Support**: Enhanced API integration capabilities

## Support and Maintenance

- **Configuration changes**: Update through standard deployment process
- **API endpoint changes**: Update baseUrl and endpoint configurations
- **Performance tuning**: Adjust timeout, retry, and batch size settings
- **Security updates**: Monitor for new vulnerability patterns and update validation logic
