package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BUILD_CUSTOM_WORKFLOW;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BUILD_MULTI_CONDITION_WORKFLOW;
import static com.intuit.appintgwkflw.wkflautomate.was.helper.TestHelper.getGlobalId;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsCustomRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.helper.TestHelper;
import com.intuit.v4.Authorization;
import com.intuit.v4.Error;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.providers.SingleResult;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Definition.TemplateInput;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefinitionProviderTest {

  private final String REMINDER = "reminder";
  private final int FIRSTITEM = 0;
  @Rule
  public ExpectedException exceptionRule = ExpectedException.none();
  @Mock
  private DefinitionService definitionService;
  @InjectMocks
  private ProviderHelper providerHelper;
  @Mock
  private TemplateDetailsRepository templateDetailsRepository;
  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock
  private AuthDetailsService authDetailsService;
  @Mock
  private DefinitionDetailsCustomRepository definitionDetailsCustomRepository;
  @Mock
  private DefinitionServiceHelper definitionServiceHelper;
  @InjectMocks
  private DefinitionProvider definitionProvider;
  @Mock
  private ProviderHelper providerHelperTest;
  @InjectMocks
  private WASBaseProvider wasBaseProvider = new DefinitionProvider(definitionService);
  @Mock
  private WASContextHandler contextHandler;
  @Mock
  private CustomWorkflowQueryCapability customWorkflowQueryCapability;
  @Mock
  private MultiStepConfig multiStepConfig;
  private Authorization authorization;
  private Definition definition;
  private Definition multiConditionDefinition;
  private Definition definitionUpdate;
  private Definition definitionDelete;
  private Definition definitionDisabled;
  private Definition definitionEnabled;
  private TemplateDetails templateDetailsFalse;
  private TemplateDetails templateDetails;
  private List<DefinitionDetails> definitionDetailsList;
  private List<DefinitionDetails> definitionDetailsListUpdate;
  private QueryHelper queryHelper;

  @Before
  public void prepareMockData() {
    authorization = TestHelper.mockAuthorization(DefinitionTestConstants.REALM_ID);
    definition = TestHelper.mockDefinitionEntity();
    definitionUpdate = TestHelper.mockDefinitionEntity();
    definitionUpdate.setId(getGlobalId("1234"));
    definitionDelete = TestHelper.mockDefinitionEntity();
    definitionDelete.setDeleted(true);

    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();

    definitionDisabled =
        TestHelper.mockDefinitionEntityWithoutWorkflowSteps(WorkflowStatusEnum.DISABLED);
    // definitionDisabled.setId(TestHelper.getGlobalId("1234"));
    definitionEnabled =
        TestHelper.mockDefinitionEntityWithoutWorkflowSteps(WorkflowStatusEnum.ENABLED);
    // definitionEnabled.setId(TestHelper.getGlobalId("1234"));
    templateDetailsFalse =
        TemplateDetails.builder()
            .id(DefinitionTestConstants.TEMPLATE_ID)
            .allowMultipleDefinitions(false)
            .build();
    templateDetails =
        TemplateDetails.builder()
            .id(DefinitionTestConstants.TEMPLATE_ID)
            .allowMultipleDefinitions(true)
            .build();
    definitionDetailsList =
        Collections.singletonList(
            TestHelper.mockDefinitionDetails(definition, templateDetailsFalse, authorization));
    definitionDetailsListUpdate =
        Collections.singletonList(
            TestHelper.mockWithDifferentDefinitionDetails(templateDetailsFalse, authorization));
    queryHelper = TestHelper.mockQueryHelperWithoutWorkflowStepsForDefintionQuery();
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testWasReadOne() {
    String id = "123";
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    GlobalId globalId = GlobalId.builder().setLocalId(id).build();
    Definition definition = SingleResult.of(Mockito.mock(Definition.class)).getResult();
    Mockito.when(definitionService.getDefinitionReadOne(eq(id), eq(globalId), eq(true)))
        .thenReturn(definition);
    SingleResult<Definition> definitionSingleResult =
        wasBaseProvider.wasReadOne(context, globalId, TestHelper.mockQueryHelperWithTemplateDataQuery());
    Assert.assertEquals(definition, definitionSingleResult.getResult());
  }

  /**
   * AllowedMultipleDefinition = false, so multiple definition will lead to an error
   */
  @Test
  public void testWasWriteOneWithAllowedMultipleDefinitionFalse() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.doReturn(Optional.of(templateDetailsFalse))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    Mockito.doReturn(Optional.of(definitionDetailsList))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_ALREADY_EXISTS.getErrorMessage());
    providerHelper.validateRequest(definition, authorization);
  }

  @Test
  public void testEnableWithAllowedMultipleDefinitionFalse() {
    templateDetailsFalse.setAllowMultipleDefinitions(false);
    definition.setId(GlobalId.builder().setLocalId("1111").build());
    Mockito.doReturn(Optional.of(definitionDetailsList))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_ALREADY_EXISTS.getErrorMessage());
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_ALREADY_EXISTS.getErrorMessage());
    providerHelper.isMultipleDefinitionAllowed(definition.getId(), templateDetailsFalse, authorization.getRealm(),
          definition.getRecordType(), true);

  }

  /**
   * With AllowedMultipleDefinition set to true.
   */
  @Test
  public void testWasWriteOneWithAllowedMultipleDefinitionTrue() {
    try {
      Mockito.doReturn(Optional.of(templateDetails))
          .when(templateDetailsRepository)
          .findTopByIdAndStatusOrderByCreatedDate(
              definition.getTemplate().getId().getLocalId(), Status.ENABLED);
      providerHelper.validateRequest(definition, authorization);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testRemoveWhitespaceFromDefinition() {
    Mockito.doReturn(Optional.of(templateDetails))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    definition.setDisplayName(" Test Definition ");
    providerHelper.validateRequest(definition, authorization);
    Assert.assertEquals("Test Definition", definition.getDisplayName());
  }

  /**
   * With AllowedMultipleDefinition set to true.
   */
  @Test
  public void testWasWriteOneWithAllowedMultipleDefinitionFalseAndNoExisitngDefinition() {
    try {
      templateDetailsFalse.setTemplateName("invoiceapproval");
      templateDetailsFalse.setRecordType(RecordType.INVOICE);
      Mockito.doReturn(Optional.of(templateDetailsFalse))
          .when(templateDetailsRepository)
          .findTopByIdAndStatusOrderByCreatedDate(
              definition.getTemplate().getId().getLocalId(), Status.ENABLED);
      Mockito.doReturn(Optional.ofNullable(null))
          .when(definitionDetailsRepository)
          .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
              Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
              templateDetailsFalse.getTemplateName(),
              false);
      providerHelper.validateRequest(definition, authorization);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testPopulateRealmInGlobalId() {
    Template template = TestHelper.mockTemplateEntity();
    template.setId(getGlobalId(DefinitionTestConstants.TEMPLATE_ID));
    List<Template> templates = Collections.singletonList(template);
    providerHelper.populateRealmInGlobalId(templates, DefinitionTestConstants.REALM_ID);
    Assert.assertNotNull(templates.get(0).getId());
  }

  @Test
  public void testWasReadListWithoutWorkflowSteps() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithoutWorkflowStepsForDefintionQuery();

    List<Definition> definitions = new ArrayList<Definition>();
    definitions.add(TestHelper.mockDefinitionEntity());
    Mockito.when(definitionService.getAllDefinitions(Mockito.any(), Mockito.any()))
        .thenReturn(new ListResult<>(definitions));

    ListResult result = wasBaseProvider.wasReadList(context, queryHelper);
    Assert.assertNotNull(result);
    Assert.assertNull(result.getError());
    Assert.assertEquals(result.getResult(), definitions);
  }

  @Test
  public void testDefinitionCreate() {
    templateDetailsFalse.setTemplateName("invoiceapproval");
    templateDetailsFalse.setRecordType(RecordType.INVOICE);
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doReturn(Optional.of(templateDetailsFalse))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    Mockito.doReturn(Optional.ofNullable(null))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    providerHelper.validateRequest(definition, authorization);
    Mockito.when(definitionService.createDefinition(definition, context.getAuthorization()))
        .thenReturn(definition);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definition);
    Assert.assertNotNull(result);
  }

  @Test
  public void testDefinitionDeleted() {
    templateDetailsFalse.setTemplateName("invoiceapproval");
    templateDetailsFalse.setRecordType(RecordType.INVOICE);
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doReturn(Optional.of(templateDetailsFalse))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    Mockito.doReturn(Optional.ofNullable(null))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    providerHelper.validateRequest(definitionDelete, authorization);
    Mockito.when(definitionService.deleteDefinition(definitionDelete, context.getAuthorization()))
        .thenReturn(definitionDelete);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definitionDelete);
    Assert.assertNotNull(result);
  }

  @Test
  public void testDefinitionDisabled() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.when(
            definitionService.disableDefinition(definitionDisabled, context.getAuthorization()))
        .thenReturn(definitionDisabled);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definitionDisabled);
    Assert.assertNotNull(result);
  }

  @Test
  public void testDefinitionEnabled() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    templateDetailsFalse.setTemplateName("invoiceapproval");
    templateDetailsFalse.setRecordType(RecordType.INVOICE);
    Mockito.doReturn(Optional.of(templateDetailsFalse))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    Mockito.doReturn(Optional.ofNullable(null))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    Mockito.when(definitionService.enableDefinition(definitionEnabled, context.getAuthorization()))
        .thenReturn(definitionEnabled);
    providerHelper.validateRequest(definitionEnabled, authorization);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definitionEnabled);
    Assert.assertNotNull(result);
  }

  @Test
  public void testHandleServiceExceptionList() {
    Error error = new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND).getError();
    ListResult<?> result =
        wasBaseProvider.handleServiceExceptionList(
            new WorkflowGeneralException(WorkflowError.DEFINITION_NOT_FOUND));

    Assert.assertNotNull(result);
    Assert.assertNotNull(error);
    Assert.assertEquals(result.getError(), error);
  }

  @Test
  public void testHandleSystemErrorList() {
    Error error =
        new Error()
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .code(WorkflowError.INTERNAL_EXCEPTION.getErrorCode())
            .message(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage());
    ListResult<?> result =
        wasBaseProvider.handleSystemErrorList(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage());
    Assert.assertNotNull(result);
    Assert.assertNotNull(error);
    Assert.assertEquals(result.getError(), error);
  }

  @Test
  public void testHandleSystemError() {
    Error error =
        new Error()
            .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
            .code(WorkflowError.INTERNAL_EXCEPTION.getErrorCode())
            .message(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage());
    SingleResult result =
        wasBaseProvider.handleSystemError(WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage());
    Assert.assertNotNull(result);
    Assert.assertNotNull(error);
    Assert.assertEquals(result.getError(), error);
  }

  @Test(expected = NullPointerException.class)
  public void testReadOne() {
    wasBaseProvider.readOne(null, null, null);
  }

  @Test(expected = NullPointerException.class)
  public void testWriteOne() {
    wasBaseProvider.writeOne(null, null);
  }

  @Test
  public void testDefinitionCreateDisabled() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    providerHelper.validateRequest(definition, authorization);
    Mockito.when(definitionService.createDefinition(definition, context.getAuthorization()))
        .thenReturn(definition);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definition);
    Assert.assertNotNull(result);
  }

  @Test
  public void testDefinitionCreateCustomDisabled() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setTemplate(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    providerHelper.validateRequest(definition, authorization);
    Mockito.when(definitionService.createDefinition(definition, context.getAuthorization()))
        .thenReturn(definition);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definition);
    Assert.assertNotNull(result);
    //For disabled custom definition create no need to check for precanned enabled definition
    Mockito.verify(definitionDetailsRepository, Mockito.times(0))
            .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    anyLong(), any(), any(), anyString(), anyBoolean());
  }

  @Test
  public void testDefinitionCreateMultiConditionEnabled_Success() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    multiConditionDefinition.setStatus(WorkflowStatusEnum.ENABLED);
    multiConditionDefinition.setDisplayName(BUILD_MULTI_CONDITION_WORKFLOW);
    Mockito.when(templateDetailsRepository
            .findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(anyString(), any(), any()))
            .thenReturn(Optional.of(templateDetails));
    providerHelper.validateRequest(multiConditionDefinition, authorization);
    Mockito.when(definitionService.createDefinition(multiConditionDefinition, context.getAuthorization()))
            .thenReturn(multiConditionDefinition);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, multiConditionDefinition);
    Assert.assertNotNull(result);
    //For disabled custom definition create no need to check for precanned enabled definition
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
            .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    anyLong(), any(), any(), anyString(), anyBoolean());
    Mockito.verify(templateDetailsRepository, Mockito.times(0))
            .findByTemplateNameAndVersion(anyString(), anyInt());
    Mockito.verify(templateDetailsRepository, Mockito.times(1))
            .findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(anyString(), any(), any());
  }

  @Test
  public void testDefinitionCreateCustomEnabled_WhenMultiConditionWorkflowsAreRolledOut_Success() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setDisplayName(BUILD_MULTI_CONDITION_WORKFLOW);
    definition.getTemplate().setName(CustomWorkflowType.APPROVAL.getTemplateName());
    definition.getWorkflowSteps().get(0).getActions().stream().findFirst().get()
            .setActionKey(CustomWorkflowType.APPROVAL.getActionKey());
    Map<String, WorkflowTemplate> workflowTemplateMap = new HashMap<>();
    WorkflowTemplate workflowTemplate = new WorkflowTemplate();
    workflowTemplate.setSingleStepVersion(1);
    workflowTemplateMap.put("customApproval",workflowTemplate);
    Mockito.when(multiStepConfig.getWorkflowTemplates())
            .thenReturn(workflowTemplateMap);
    Mockito.when(templateDetailsRepository
                    .findByTemplateNameAndVersion(anyString(), anyInt()))
            .thenReturn(Optional.of(templateDetails));

    providerHelper.validateRequest(definition, authorization);
    Mockito.when(definitionService.createDefinition(definition, context.getAuthorization()))
            .thenReturn(definition);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definition);
    Assert.assertNotNull(result);
    //For disabled custom definition create no need to check for precanned enabled definition
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
            .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    anyLong(), any(), any(), anyString(), anyBoolean());
    Mockito.verify(templateDetailsRepository, Mockito.times(1))
            .findByTemplateNameAndVersion(anyString(), anyInt());
    Mockito.verify(templateDetailsRepository, Mockito.times(0))
            .findTopByTemplateNameAndStatusOrderByCreatedDateDesc(anyString(), any());
  }

  @Test
  public void testDefinitionCreateCustomDisabledWithNoAction() {
    RequestContext context = Mockito.mock(RequestContext.class);
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setTemplate(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    definition.getWorkflowSteps().get(0).setActions(null);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.INVALID_ACTION_KEY.getErrorMessage());
    providerHelper.validateRequest(definition, authorization);
    Mockito.verify(definitionDetailsRepository, Mockito.times(0))
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    anyLong(), any(), any(), anyString(), anyBoolean());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDefinitionUpdateDifferentIds() {
    RequestContext context = Mockito.mock(RequestContext.class);
    context.setAuthorization(authorization);
    Mockito.doReturn(Optional.of(templateDetailsFalse))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    Mockito.doReturn(Optional.ofNullable(definitionDetailsList))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    providerHelper.validateRequest(definitionUpdate, authorization);

    Mockito.when(definitionService.updateDefinition(definitionUpdate, context.getAuthorization()))
        .thenReturn(definitionUpdate);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definitionUpdate);
    Assert.assertNotNull(result);
  }

  @Test
  public void testDefinitionUpdate() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doReturn(Optional.of(templateDetailsFalse))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    Mockito.doReturn(Optional.ofNullable(definitionDetailsListUpdate))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    providerHelper.validateRequest(definitionUpdate, authorization);
    Mockito.when(definitionService.updateDefinition(definitionUpdate, context.getAuthorization()))
        .thenReturn(definitionUpdate);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definitionUpdate);
    Assert.assertNotNull(result);
  }

  @Test
  public void testDefinitionUpdateNoEnabled() {
    templateDetailsFalse.setTemplateName("invoiceapproval");
    templateDetailsFalse.setRecordType(RecordType.INVOICE);
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doReturn(Optional.of(templateDetailsFalse))
        .when(templateDetailsRepository)
        .findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    Mockito.doReturn(Optional.ofNullable(null))
        .when(definitionDetailsRepository)
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
            templateDetailsFalse.getTemplateName(),
            false);
    providerHelper.validateRequest(definitionUpdate, authorization);
    Mockito.when(definitionService.updateDefinition(definitionUpdate, context.getAuthorization()))
        .thenReturn(definitionUpdate);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definitionUpdate);
    Assert.assertNotNull(result);
  }

  /**
   * Testing migration case for update definition from precanned to custom workflow
   */
  @Test
  public void testPrecannedToCustomDefinitionUpdate() {
    templateDetails.setTemplateName("customApproval");
    templateDetails.setRecordType(RecordType.INVOICE);
    definitionUpdate.setStatus(WorkflowStatusEnum.ENABLED);
    definitionUpdate.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    definitionUpdate.getTemplate().setCategory(TemplateCategory.CUSTOM.toString());
    definitionUpdate.getTemplate().setName("customApproval");
    definitionUpdate.getWorkflowSteps().get(0).getActions().get(0).setActionKey("approval");
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doReturn(Optional.of(templateDetails))
            .when(templateDetailsRepository)
            .findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(
                    "customApproval", Status.ENABLED, Collections.singletonList(-1));
    Mockito.doReturn(Optional.ofNullable(null))
            .when(definitionDetailsRepository)
            .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    Long.valueOf(authorization.getRealm()), ModelType.BPMN, RecordType.INVOICE,
                    templateDetails.getTemplateName(),
                    false);
    Mockito.when(customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(definitionUpdate))
            .thenReturn(true);
    providerHelper.validateRequest(definitionUpdate, authorization);
    Mockito.when(definitionService.updateDefinition(definitionUpdate, context.getAuthorization()))
            .thenReturn(definition);
    SingleResult<Definition> result = wasBaseProvider.wasWriteOne(context, definitionUpdate);
    Assert.assertNotNull(result);
  }

  /**
   * Error Scenario for Invalid Action Key displayName.
   */
  @Test
  public void testCustomWorkflow_InvalidActionKey_ThrowsException() {
    definition.setTemplate(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    templateDetails.setTemplateName("byoreminder");
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.INVALID_ACTION_KEY.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Error Scenario for Invalid Action Key for multi condition approval workflow
   */
  @Test
  public void testMultiCondition_InvalidActionKey_ThrowsException() {
    definition.setTemplate(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    templateDetails.setTemplateName("customApproval");
    List<WorkflowStep.StepNext> nexts = new ArrayList<>();
    nexts.add(new WorkflowStep.StepNext());
    definition.getWorkflowSteps().stream().findFirst().get().setNext(nexts);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.INVALID_ACTION_KEY.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Error Scenario for null or empty displayName.
   */
  @Test
  public void testCustomWorkflow_InvalidDisplayName_ThrowsException() {
    definition.setTemplate(null);
    definition.setDisplayName(null);
    setActionInDefinition(definition, "reminder");
    templateDetails.setTemplateName("byoreminder");
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DISPLAY_NAME_NOT_FOUND.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Error Scenario for Empty Rules.
   */
  @Test
  public void testCustomWorkflow_EmptyRule_ThrowsException() {
    definition.setTemplate(null);
    definition.setWorkflowSteps(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    templateDetails.setTemplateName("customReminder");
    setActionInDefinition(definition, "reminder");
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.RULES_NOT_FOUND.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  @Test
  public void testMultiConditionWorkflow_WithNoConditionStep() {
    definition = TestHelper.mockSingleStepMultiConditionDefinitionEntity();
    definition.setDisplayName("Single Step Multi Condition Workflow");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(
        Mockito.anyString(), Mockito.any(), any())).thenReturn(Optional.ofNullable(templateDetails));
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Error Scenario for Name Validation in Create Definition.
   */
  @Test
  public void testCustomWorkflow_NameValidation_CreateDefinition_ThrowsException() {
    definition.setTemplate(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    templateDetails.setTemplateName("customReminder");
    Mockito.when(
            definitionDetailsRepository.findDefinitionsForCustomWorkflow(
                Mockito.anyString(), Mockito.any()))
        .thenReturn(
            Optional.ofNullable(
                Collections.singletonList(
                    TestHelper.mockDefinitionDetailsObject(
                        1234L, "456", Status.ENABLED, BUILD_CUSTOM_WORKFLOW))));
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_NAME_ALREADY_EXISTS.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Error Scenario for Name Validation in Update Definition. Testing for cases like, while
   * updating, we found multiple definitions present already with the same name. However, in case of
   * Custom Workflow(s) this will not happen as we are pre-validating in Create Definition Flows.
   */
  @Test
  public void
  testCustomWorkflow_NameValidation_UpdateDefinition_MultipleDefinitionAlreadyFound_ThrowsException() {
    definition.setTemplate(null);
    definition.setId(getGlobalId("123"));
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    templateDetails.setTemplateName("byoreminder");
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");

    DefinitionDetails def1 =
        TestHelper.mockDefinitionDetailsObject(123L, "123", Status.ENABLED, BUILD_CUSTOM_WORKFLOW);

    DefinitionDetails def2 =
        TestHelper.mockDefinitionDetailsObject(123L, "456", Status.ENABLED, BUILD_CUSTOM_WORKFLOW);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(def1);
    definitionDetailsList.add(def2);
    Mockito.when(
            definitionDetailsRepository.findDefinitionsForCustomWorkflow(
                Mockito.anyString(), Mockito.any()))
        .thenReturn(Optional.ofNullable(definitionDetailsList));
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_NAME_ALREADY_EXISTS.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Error Scenario for Name Validation in Update Definition. Scenario where will updating the
   * definition, we provide a name which matches to that of some other enabled definition.
   */
  @Test
  public void testCustomWorkflow_NameValidation_UpdateDefinition_ThrowsException() {
    definition.setTemplate(null);
    definition.setId(getGlobalId("123"));
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    templateDetails.setTemplateName("customReminder");

    DefinitionDetails def1 =
        TestHelper.mockDefinitionDetailsObject(
            123L, "456", Status.ENABLED, BUILD_CUSTOM_WORKFLOW); // DefinitionId is different

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(def1);
    Mockito.when(
            definitionDetailsRepository.findDefinitionsForCustomWorkflow(
                Mockito.anyString(), Mockito.any()))
        .thenReturn(Optional.ofNullable(definitionDetailsList));
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_NAME_ALREADY_EXISTS.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Success Scenario for Name Validation in Update Definition and failing with Invalid Key later.
   * It will pass the name validation and will fail with Invalid Action Key
   */
  @Test
  public void test_NameValidationSuccess_InvalidActionKey_ThrowsException() {
    definition.setTemplate(null);
    templateDetails.setTemplateName("byoreminder");
    definition.setId(getGlobalId("123"));
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);

    DefinitionDetails def1 =
        TestHelper.mockDefinitionDetailsObject(
            123L,
            "123",
            Status.ENABLED,
            BUILD_CUSTOM_WORKFLOW); // Definition id is same, case of update Definition

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(def1);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.INVALID_ACTION_KEY.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Success Scenario for Name Validation in Update Definition and failing with Invalid Key later.
   * It will pass the name validation and will fail with Invalid Action Key. Here, Definition
   * initally was created in Disabled Mode and Updated Later
   */
  @Test
  public void test_NameValidationSuccess2_InvalidActionKey_ThrowsException() {
    definition.setTemplate(null);
    templateDetails.setTemplateName("byoreminder");
    definition.setId(getGlobalId("123"));
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);

    DefinitionDetails def1 =
        TestHelper.mockDefinitionDetailsObject(
            123L,
            "123",
            Status.DISABLED,
            BUILD_CUSTOM_WORKFLOW); // Definition id is same, case of update Definition

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(def1);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.INVALID_ACTION_KEY.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Error Scenario for Name Validation in Create Definition. It fails the name validation ignoring
   * leading and trailing spaces as well as the case of supplied Display Name String.
   */
  @Test
  public void
  testCustomWorkflow_NameValidationSuccessCaseInsensitive_CreateDefinition_ThrowsException() {
    definition.setTemplate(null);
    definition.setDisplayName("custom workflow 12");
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    templateDetails.setTemplateName("customReminder");

    Mockito.when(
            definitionDetailsRepository.findDefinitionsForCustomWorkflow(
                Mockito.anyString(), Mockito.any()))
        .thenReturn(
            Optional.ofNullable(
                Collections.singletonList(
                    TestHelper.mockDefinitionDetailsObject(
                        1234L, "456", Status.ENABLED, "  Custom Workflow 12 "))));
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_NAME_ALREADY_EXISTS.getErrorDescription());
    providerHelper.validateRequest(definition, authorization);
  }

  @Test
  public void testProviderHelperWorkflowSteps() {
    boolean result = providerHelper.checkForWorkflowSteps(
        TestHelper.mockQueryHelperWithWorkflowStepsForTemplateQuery());
    Assert.assertTrue(result);
  }

  @Test
  public void testProviderhelperDefinitionNotActive() {
    Mockito.doReturn(Optional.of(definitionDetailsList))
        .when(templateDetailsRepository)
        .findById(Mockito.any());
    WorkflowStep.ActionMapper actionMapper = new WorkflowStep.ActionMapper();
    actionMapper.setActionKey(REMINDER);
    List<WorkflowStep.ActionMapper> actions = new ArrayList<>();
    actions.add(actionMapper);
    definition.getWorkflowSteps().get(FIRSTITEM).setActions(actions);
    Optional<TemplateDetails> templateDetails = providerHelper.getTemplateDetails(definition);
    Assert.assertNotNull(templateDetails);
  }

  @Test
  public void testProviderHelperGetTemplateDetailsRealmUser() {
    WorkflowStep.ActionMapper actionMapper = new WorkflowStep.ActionMapper();
    actionMapper.setActionKey(REMINDER);
    List<WorkflowStep.ActionMapper> actions = new ArrayList<>();
    actions.add(actionMapper);
    Template template = new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID)).name("customReminder").category("CUSTOM");
    definition.setTemplate(template);
    definition.getWorkflowSteps().get(FIRSTITEM).setActions(actions);
    WASContext.setMigrationContext(true);
    try {
      Optional<TemplateDetails> templateDetails = providerHelper.getTemplateDetails(definition);
    }finally{
      WASContext.clear();
    }
    Assert.assertNotNull(templateDetails);
  }

  @Test
  public void testPopulateTemplateNotFoundException() {
    Template template = TestHelper.mockTemplateEntity();
    template.setId(getGlobalId(DefinitionTestConstants.TEMPLATE_ID));
    template.isDeleted();
    List<Template> templates = Collections.singletonList(template);
    definition =
        new Definition()
            .name(DefinitionTestConstants.DEFINITION_NAME)
            .template(template)
            .recordType(RecordType.INVOICE.getRecordType());
    try {
      definition.setWorkflowSteps(Collections.singletonList(TestHelper.createMultiActionStep("action")));
      providerHelper.validateRequest(definition, authorization);
      Assert.fail("Above method will throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.TEMPLATE_NOT_FOUND.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testCreateDefinitionWithDefaultValuesSuccess() {
    String templateName = "testTemplate";
    Definition.TemplateInput templateInput = new Definition.TemplateInput();
    templateInput.setName(templateName);
    RequestContext context = Mockito.mock(RequestContext.class);
    Definition definition = new Definition().name("testDefinition");
    Definition definitionCreatedAsSystemUser = new Definition().name("testSystemDefinition");

    Mockito.when(definitionService.createDefinitionWithDefaultValues(templateName, false, authorization))
        .thenReturn(definition);
    Mockito.when(definitionService.createDefinitionWithDefaultValues(templateName, true, authorization))
        .thenReturn(definitionCreatedAsSystemUser);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doNothing().when(contextHandler)
        .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    SingleResult<Definition> definitionActual = definitionProvider.createDefinitionWithDefaultValues(
        context, templateInput);
    Assert.assertNotNull(definitionActual.getResult());
    Mockito.verify(contextHandler, Mockito.atLeast(1)).addKey(Mockito.any(), Mockito.anyString());
    Assert.assertEquals("testDefinition", definitionActual.getResult().getName());

    //Create definition as System User
    templateInput.setCreatedAsSystemUser(true);
    SingleResult<Definition> definitionActualWithSystemCreated = definitionProvider.createDefinitionWithDefaultValues(
        context, templateInput);
    Mockito.verify(contextHandler, Mockito.atLeast(1)).addKey(Mockito.any(), Mockito.anyString());
    Assert.assertEquals("testSystemDefinition", definitionActualWithSystemCreated.getResult().getName());
  }

  @Test
  public void testCreateDefinitionWithDefaultValuesException() {
    String templateName = "testTemplate";
    Definition.TemplateInput templateInput = new Definition.TemplateInput();
    templateInput.setName(templateName);
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(definitionService.createDefinitionWithDefaultValues(templateName, false, authorization))
        .thenThrow(new WorkflowGeneralException(WorkflowError.TEMPLATE_DOES_NOT_EXIST));
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doNothing().when(contextHandler)
        .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    SingleResult<Definition> definitionActual = definitionProvider.createDefinitionWithDefaultValues(
        context, templateInput);
    Mockito.verify(contextHandler, Mockito.atLeast(1)).addKey(Mockito.any(), Mockito.anyString());
    Assert.assertNotNull(definitionActual.getError());
    Assert.assertEquals(WorkflowError.TEMPLATE_DOES_NOT_EXIST.name(),
        definitionActual.getError().getMessage());
  }

  @Test
  public void testCreateDefinitionWithDefaultValuesUnknownException() {
    String templateName = "testTemplate";
    Definition.TemplateInput templateInput = new Definition.TemplateInput();
    templateInput.setName(templateName);
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(definitionService.createDefinitionWithDefaultValues(templateName, false, authorization))
        .thenThrow(new NullPointerException());
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doNothing().when(contextHandler)
        .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    SingleResult<Definition> definitionActual = definitionProvider.createDefinitionWithDefaultValues(
        context, templateInput);
    Mockito.verify(contextHandler, Mockito.atLeast(1)).addKey(Mockito.any(), Mockito.anyString());
    Assert.assertNotNull(definitionActual.getError());
    Assert.assertEquals(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage(),
        definitionActual.getError().getMessage());
  }

  @Test
  public void testCustomApprovalMultipleDefValidation() {
    Template template = TestHelper.mockTemplateEntity();
    template.setName("customApproval");
    template.setId(getGlobalId(DefinitionTestConstants.TEMPLATE_ID));
    template.isDeleted();
    List<Template> templates = Collections.singletonList(template);

    definition =
        new Definition()
            .name(DefinitionTestConstants.DEFINITION_NAME)
            .template(template)
            .recordType(RecordType.BILL.getRecordType())
            .status(WorkflowStatusEnum.ENABLED)
            .displayName("displayName")
            .id(getGlobalId("def-id"));

    Definition definition1 =
        new Definition()
            .name(DefinitionTestConstants.DEFINITION_NAME)
            .template(template)
            .recordType(RecordType.BILL.getRecordType())
            .status(WorkflowStatusEnum.ENABLED)
            .displayName("displayName")
            .id(getGlobalId("def-id-1"));

    setActionInDefinition(definition, "approval");
    setActionInDefinition(definition1, "approval");
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(null);
    definition.getWorkflowSteps(0).getWorkflowStepCondition().getRuleLines().get(0).setRules(rules);
    definition1.getWorkflowSteps(0).getWorkflowStepCondition().getRuleLines().get(0)
        .setRules(rules);
    definition1.getWorkflowSteps(0).setId(getGlobalId("customStartEvent"));
    templateDetails.setTemplateName("customApproval");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(
        Mockito.anyString(), Mockito.any(), any())).thenReturn(Optional.ofNullable(templateDetails));

    when(definitionDetailsRepository
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Mockito.anyLong(), Mockito.any(), Mockito.any(), Mockito.anyString(),
            Mockito.anyBoolean()))
        .thenReturn(Optional.empty())
        .thenReturn(Optional.of(definitionDetailsList));

    try {
      providerHelper.validateRequest(definition1, authorization);
      Assert.fail("Above method will throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage().contains("Error Definition Already Exists for Template"));
    }
  }

  @Test
  public void testCustomApprovalUpdateMultipleDefValidation() {
    Template template = TestHelper.mockTemplateEntity();
    template.setName("customApproval");
    template.setId(getGlobalId(DefinitionTestConstants.TEMPLATE_ID));
    template.isDeleted();
    List<Template> templates = Collections.singletonList(template);

    definition =
        new Definition()
            .name(DefinitionTestConstants.DEFINITION_NAME)
            .template(template)
            .recordType(RecordType.BILL.getRecordType())
            .status(WorkflowStatusEnum.ENABLED)
            .displayName("displayName")
            .id(getGlobalId("def-id"));

    setActionInDefinition(definition, "approval");
    List<RuleLine.Rule> rules = new ArrayList<>();
    rules.add(null);
    definition.getWorkflowSteps(0).getWorkflowStepCondition().getRuleLines().get(0).setRules(rules);
    definition.getWorkflowSteps(0).setId(getGlobalId("customStartEvent"));
    templateDetails.setTemplateName("customApproval");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(
        Mockito.anyString(), Mockito.any(), any())).thenReturn(Optional.ofNullable(templateDetails));

    Optional<List<DefinitionDetails>> optionalDefinitionDetails = Optional.of(new ArrayList<>());
    when(definitionDetailsRepository
        .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
            Mockito.anyLong(), Mockito.any(), Mockito.any(), Mockito.anyString(),
            Mockito.anyBoolean()))
        .thenReturn(optionalDefinitionDetails)
        .thenReturn(Optional.of(definitionDetailsList));


    definition.setId(getGlobalId("def-id"));
    // no exception is thrown for update case with same definition id as enabled list
    providerHelper.validateRequest(definition, authorization);

    // exception thrown for update case with different def id then enabled list
    definition.setId(getGlobalId("def-different-id"));
    try {
      providerHelper.validateRequest(definition, authorization);
      Assert.fail("Above method will throw exception");
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage().contains("Error Definition Already Exists for Template"));
    }
  }

  @Test
  public void readOneDefinition_nonRealm() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    GlobalId id = Mockito.mock(GlobalId.class);
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Mockito.doNothing().when(contextHandler).addKey(any(), any());
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    SingleResult<Definition> result = null;
    try {
      result = definitionProvider.readOne(requestContext, id, queryHelper);
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  @Test
  public void readListDefinition_nonRealm() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Mockito.doNothing().when(contextHandler).addKey(any(), any());
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    ListResult<Definition> result = null;
    try {
      result = definitionProvider.readList(requestContext, queryHelper);
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  @Test
  public void writeOneDefinition_nonRealm() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    Mockito.doNothing().when(contextHandler).addKey(any(), any());
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    SingleResult<Definition> result = null;
    try {
      result = definitionProvider.writeOne(requestContext, new Definition());
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  @Test
  public void createDefinitionDefaultValues_nonRealm() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    SingleResult<Definition> result = null;
    try {
      result = definitionProvider.createDefinitionWithDefaultValues(requestContext,
          new TemplateInput());
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  /**
   * Test create custom workflow definition where precanned definiton already present for same
   * workflow
   */
  @Test
  public void testCreatePrecannedDefinitionWithCustomPresent() {
    templateDetails.setTemplateName("invoiceapproval");
    templateDetails.setRecordType(RecordType.INVOICE);
    templateDetails.setAllowMultipleDefinitions(false);
    Mockito.when(templateDetailsRepository.findTopByIdAndStatusOrderByCreatedDate(
        "1234", Status.ENABLED)).thenReturn(Optional.ofNullable(templateDetails));
    Mockito.when(
            definitionDetailsRepository.findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
                1234L, RecordType.INVOICE, "customApproval", Status.ENABLED, ModelType.BPMN))
        .thenReturn(
            Optional.of(definitionDetailsList));

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_ALREADY_EXISTS.getErrorMessage());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Test create custom workflow definition where precanned definiton already present for same
   * workflow
   */
  @Test
  public void testCreatePrecannedDefinitionWithNoCustomPresent() {
    templateDetails.setTemplateName("invoiceapproval_QBDT");
    templateDetails.setRecordType(RecordType.INVOICE);
    templateDetails.setAllowMultipleDefinitions(false);
    Mockito.when(templateDetailsRepository.findTopByIdAndStatusOrderByCreatedDate(
        "1234", Status.ENABLED)).thenReturn(Optional.ofNullable(templateDetails));

    providerHelper.validateRequest(definition, authorization);
    Mockito.verify(definitionDetailsRepository, Mockito.times(0))
        .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
            1234L, RecordType.INVOICE, "customApproval", Status.ENABLED, ModelType.BPMN);
  }

  /**
   * Test create custom workflow definition where precanned definiton already present for same
   * workflow
   */
  @Test
  public void testCreateCustomDefinitionWithPrecannedPresent() {
    definition.setTemplate(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.getWorkflowSteps().get(0).getActions().get(0).setActionKey("reminder");
    templateDetails.setTemplateName("customReminder");
    Mockito.when(
            definitionDetailsRepository.findDefinitionsForCustomWorkflow(
                Mockito.anyString(), Mockito.any()))
        .thenReturn(
            Optional.empty());
    Optional<List<DefinitionDetails>> optionalDefinitionDetails = Optional.of(Collections.singletonList(
            TestHelper.mockDefinitionDetails(definition, templateDetailsFalse, authorization)));
    Mockito.when(definitionDetailsRepository.findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    anyLong(), any(), any(), anyString(), anyBoolean()))
            .thenReturn(optionalDefinitionDetails);
    Mockito.when(customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(definition))
            .thenReturn(false);
    definition.setId(getGlobalId("def-id"));
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.DEFINITION_ALREADY_EXISTS.getErrorMessage());
    providerHelper.validateRequest(definition, authorization);
  }

  /**
   * Test create custom workflow definition where precanned definiton already present for same
   * workflow
   */
  @Test
  public void testCreateCustomDefinitionWithPrecannedPresentForEmptyAction() {
    definition.setTemplate(null);
    definition.setDisplayName(BUILD_CUSTOM_WORKFLOW);
    definition.getWorkflowSteps().get(0).setActions(new ArrayList<ActionMapper>());
    templateDetails.setTemplateName("customReminder");
    Mockito.when(
            definitionDetailsRepository.findDefinitionsForCustomWorkflow(
                Mockito.anyString(), Mockito.any()))
        .thenReturn(
            Optional.empty());

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.INVALID_ACTION_KEY.getErrorMessage());
    providerHelper.validateRequest(definition, authorization);
    Mockito.verify(definitionDetailsRepository, Mockito.times(0))
            .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    anyLong(), any(), any(), anyString(), anyBoolean());
  }

  @Test
  public void readOneDefinitionObfuscated_nonRealm() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    ListResult<Definition> result = null;
    try {
      result = definitionProvider.getDefinitionDetails(requestContext, queryHelper);
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  @Test
  public void readOneDefinitionObfuscated_success() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithTemplateDataQuery();
    when(requestContext.getAuthorization()).thenReturn(authorization);
    ListResult<Definition> result = ListResult.of(Collections.singletonList(definition));
    when(definitionService.getDefinitionWithObfuscatedValues(requestContext, queryHelper, true))
        .thenReturn(result.getResult().get(0));
    try {
      result = definitionProvider.getDefinitionDetails(requestContext, queryHelper);
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
  }

  @Test
  public void readOneDefinitionObfuscated_Error() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(false);
    ListResult<Definition> result = null;
    try {
      result = definitionProvider.getDefinitionDetails(requestContext, queryHelper);
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(
        WorkflowError.INTERNAL_EXCEPTION.getErrorMessage(), result.getError().getMessage());
  }

  private void setActionInDefinition(Definition definition, String actionKey) {
    WorkflowStep step = new WorkflowStep();
    ActionMapper action = new ActionMapper();
    WorkflowStepCondition condition = new WorkflowStepCondition();
    RuleLine ruleLine = new RuleLine();
    List<RuleLine> ruleLines = new ArrayList<>();
    ruleLines.add(ruleLine);
    condition.setRuleLines(ruleLines);
    action.setActionKey(actionKey);
    step.setId(getGlobalId("1234"));
    step.getActions().add(action);
    step.setWorkflowStepCondition(condition);
    definition.setWorkflowSteps(0, step);
    definition.setStatus(WorkflowStatusEnum.ENABLED);
  }

  @Test
  public void wasWriteOne_testDriveRealm() {
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=**********");
    Mockito.doNothing().when(contextHandler).addKey(any(), any());
    Authorization authorization1=  TestHelper.mockAuthorization("**********");
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization1);
    SingleResult<Definition> result = null;
    try {
      result = definitionProvider.writeOne(requestContext, new Definition());
    } finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.CREATION_NOT_SUPPORTED.name(), result.getError().getMessage());
  }
}
