# Report Access Validation Configuration
# This configuration enables validation of report IDs to prevent cross-realm access vulnerabilities

report-access-validation:
  # Enable/disable report access validation
  enabled: true
  
  # Base URL for QuickBooks API or report validation service
  baseUrl: https://sandbox-quickbooks.api.intuit.com
  
  # Endpoint for validating individual report access
  # Supports placeholders: {realmId}, {reportId}
  reportValidationEndpoint: /v3/companyinfo/{realmId}/reports/{reportId}/validate
  
  # API call timeout in milliseconds
  timeoutMs: 5000
  
  # Maximum number of retries for failed API calls
  maxRetries: 3
  
  # List of report types that require validation
  supportedReportTypes:
    - invoice
    - bill
    - payment
    - estimate
    - customer
    - vendor
    - item
    - account
    - report
  
  # Enable batch validation for multiple reports
  batchValidationEnabled: true
  
  # Maximum number of reports in a single batch request
  maxBatchSize: 50
  
  # Cache settings for validation results
  cachingEnabled: true
  cacheTtlSeconds: 300  # 5 minutes

# Environment-specific configurations
---
spring:
  profiles: local
report-access-validation:
  baseUrl: http://localhost:8080/mock-qb-api
  enabled: false  # Disable in local development

---
spring:
  profiles: qal
report-access-validation:
  baseUrl: https://qal-quickbooks.api.intuit.com
  enabled: true

---
spring:
  profiles: e2e
report-access-validation:
  baseUrl: https://e2e-quickbooks.api.intuit.com
  enabled: true

---
spring:
  profiles: prd
report-access-validation:
  baseUrl: https://quickbooks.api.intuit.com
  enabled: true
  # More conservative settings for production
  timeoutMs: 3000
  maxRetries: 2
  cacheTtlSeconds: 600  # 10 minutes
