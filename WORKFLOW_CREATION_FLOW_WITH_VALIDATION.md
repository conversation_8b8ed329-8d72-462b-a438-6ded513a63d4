# Workflow Creation Flow with Report Access Validation

## Overview
This document explains exactly **when** and **how** our report access validation integrates into the existing workflow creation process, and **how** the workflow continues to be created after successful verification.

## Complete Flow Breakdown

### 1. API Entry Point
```
POST /v1/definitions
```
- **Controller**: Receives the workflow creation request
- **Authorization**: User's realm/company ID is extracted from authorization headers
- **Payload**: Contains workflow definition with notification variables (potentially including report IDs)

### 2. Definition Service Processing
```java
// DefinitionServiceImpl.createDefinition()
public Definition createDefinition(Definition definition, Authorization authorization) {
    providerHelper.validateRequest(definition, authorization);  // Basic validation
    
    final DefinitionInstance definitionInstance = 
        getCreateDefinitionHandler(definition)
            .process(buildDefinitionInstance(definition), authorization.getRealm());
    
    return definitionServiceHelper.executeAsyncWorkflowTasks(definitionInstance, authorization, false);
}
```

### 3. Definition Handler Processing
```java
// CreateDefinitionHandler.process()
public DefinitionInstance process(DefinitionInstance definitionInstance, String realmId) {
    // Process each workflow step
    definitionInstance.getDefinition().getWorkflowSteps().forEach(
        workflowStep -> processWorkflowStep(workflowStep, ...)
    );
    
    // Extract placeholder values (THIS IS WHERE OUR VALIDATION HAPPENS)
    Map<String, Object> placeholders = placeholderExtractor.extractPlaceholderValue(definitionInstance);
    definitionInstance.setPlaceholderValue(placeholders);
    
    return definitionInstance;
}
```

### 4. **CRITICAL POINT: Report Validation Integration**

#### 4.1 Placeholder Extraction Phase
```java
// PlaceholderExtractor calls NotificationTaskRequestModifier
public WorkflowTaskRequest getTaskRequest(WorkflowTaskRequest taskRequest, WorkerActionRequest workerActionRequest) {
    // Get input variables from workflow definition
    Map<String, String> inputMap = getInputMap(parameterDetailsMap.get(), workerActionRequest.getInputVariables());
    
    // Get realm ID for validation
    String realmId = String.valueOf(workerActionRequest.getOwnerId());
    
    // *** REPORT VALIDATION HAPPENS HERE ***
    Map<String, String> substitutedInputMap = 
        SubstitutePlaceholderUtil.substitutePlaceholderWithValidation(inputMap, false, realmId, reportIdValidator);
    
    populateNotificationDetails(taskRequest, substitutedInputMap);
    return taskRequest;
}
```

#### 4.2 Report Validation Process
```java
// ReportIdValidator.validateReportIdsInVariables()
public void validateReportIdsInVariables(Map<String, String> inputVariables, String realmId) {
    // 1. Extract report IDs from variables using regex patterns
    List<ReportIdInfo> extractedReportIds = extractReportIds(inputVariables);
    
    // 2. Group by report type for batch processing
    Map<String, List<String>> reportIdsByType = groupReportIdsByType(extractedReportIds);
    
    // 3. Validate each group via API calls
    for (Map.Entry<String, List<String>> entry : reportIdsByType.entrySet()) {
        String reportType = entry.getKey();
        List<String> reportIds = entry.getValue();
        
        Map<String, Boolean> validationResults = 
            reportAccessService.validateMultipleReportAccess(reportIds, realmId, reportType);
        
        // 4. Check for any failed validations
        for (Map.Entry<String, Boolean> result : validationResults.entrySet()) {
            if (!Boolean.TRUE.equals(result.getValue())) {
                // *** SECURITY EXCEPTION THROWN HERE ***
                throw new WorkflowGeneralException(
                    WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS,
                    "Access denied for report ID: " + result.getKey()
                );
            }
        }
    }
}
```

#### 4.3 API Call to QuickBooks
```java
// ReportAccessServiceImpl.validateReportAccess()
public boolean validateReportAccess(String reportId, String realmId, String reportType) {
    // Build API request
    String validationUrl = reportAccessConfig.getValidationUrl(realmId, reportId);
    // Example: https://quickbooks.api.intuit.com/v3/companyinfo/67890/reports/12345/validate
    
    WASHttpRequest<Void, ReportValidationResponse> request = WASHttpRequest.builder()
        .url(validationUrl)
        .httpMethod(HttpMethod.GET)
        .requestHeaders(headerPopulator.populateHeaders())
        .responseType(new ParameterizedTypeReference<ReportValidationResponse>() {})
        .build();
    
    // Make API call with retry logic
    WASHttpResponse<ReportValidationResponse> response = reportAccessClient.httpResponse(request);
    
    // Process response
    if (response.getStatus() == HttpStatus.OK && response.getResponse() != null) {
        return response.getResponse().isHasAccess(); // TRUE/FALSE based on API response
    }
    
    return false; // Deny access on any error (fail-safe)
}
```

### 5. Two Possible Outcomes

#### 5.1 **VALIDATION FAILS** ❌
```
ReportAccessService returns FALSE
    ↓
ReportIdValidator throws WorkflowGeneralException(UNAUTHORIZED_RESOURCE_ACCESS)
    ↓
Exception propagates up the call stack
    ↓
NotificationTaskRequestModifier → PlaceholderExtractor → CreateDefinitionHandler → DefinitionService → Controller
    ↓
HTTP 403 Forbidden returned to user
    ↓
WORKFLOW CREATION STOPS - No database save, no Camunda deployment, no AppConnect registration
```

#### 5.2 **VALIDATION SUCCEEDS** ✅
```
ReportAccessService returns TRUE for all report IDs
    ↓
ReportIdValidator validation passes
    ↓
SubstitutePlaceholderUtil continues with normal placeholder substitution
    ↓
NotificationTaskRequestModifier completes processing
    ↓
PlaceholderExtractor returns processed placeholders
    ↓
CreateDefinitionHandler completes definition processing
    ↓
DefinitionService.executeAsyncWorkflowTasks() is called
    ↓
WORKFLOW CREATION CONTINUES NORMALLY
```

### 6. **AFTER SUCCESSFUL VALIDATION: Normal Workflow Creation**

#### 6.1 Async Workflow Tasks Execution
```java
// DefinitionServiceHelper.executeAsyncWorkflowTasks()
public Definition executeAsyncWorkflowTasks(DefinitionInstance definitionInstance, Authorization authorization, boolean isUpdate) {
    
    // Parallel execution of three tasks:
    
    // Task 1: Deploy to Camunda Engine
    State camundaState = new RxExecutionChain(inputState)
        .next(new DeployDefinitionTask(bpmnEngineDefinitionServiceRest))
        .execute();
    
    // Task 2: Save to WAS Database
    State dbState = new RxExecutionChain(inputState)
        .next(new SaveDefinitionInDataStoreTask(definitionServiceHelper))
        .execute();
    
    // Task 3: Register with AppConnect
    State appConnectState = new RxExecutionChain(inputState)
        .next(new SaveAndActivateAppConnectWorkflowTask(appConnectService))
        .execute();
    
    return buildDefinitionResponse(definitionInstance);
}
```

#### 6.2 Database Persistence
```java
// SaveDefinitionInDataStoreTask.execute()
public State execute(State inputRequest) {
    DefinitionDetails definitionDetails = buildDefinitionDetails(definitionInstance);
    definitionDetails.setOwnerId(Long.parseLong(realmId)); // Realm association
    definitionDetails.setPlaceholderValue(placeholderValue); // Validated placeholders
    
    // Save to database
    definitionDetailsRepository.save(definitionDetails);
    
    return state;
}
```

#### 6.3 Camunda Deployment
```java
// DeployDefinitionTask.execute()
public State execute(State inputRequest) {
    BpmnModelInstance bpmnModel = inputRequest.getValue(BPMN_MODEL_INSTANCE);
    
    // Deploy to Camunda with validated placeholders
    DeploymentResponse response = bpmnEngineDefinitionServiceRest.deployDefinition(
        definitionId, 
        Bpmn.convertToString(bpmnModel)
    );
    
    return state.addValue(DEPLOYMENT_ID, response.getDeploymentId());
}
```

#### 6.4 AppConnect Registration
```java
// SaveAndActivateAppConnectWorkflowTask.execute()
public State execute(State inputRequest) {
    // Register workflow with AppConnect for event processing
    AppConnectSaveWorkflowResponse response = appConnectService.createWorkflow(
        subscriptionId,
        definitionId,
        bpmnModelInstance,
        definitionName
    );
    
    return state.addValue(WORKFLOW_ID, response.getWorkflowId());
}
```

## Key Integration Points

### 1. **When Validation Occurs**
- **Timing**: During placeholder extraction phase, BEFORE any persistence
- **Location**: `NotificationTaskRequestModifier.getTaskRequest()`
- **Trigger**: When processing notification variables that contain report IDs

### 2. **What Gets Validated**
- Report IDs in placeholder patterns: `[[report:123]]`, `[[invoice:456]]`
- Report IDs in variable names: `reportId`, `invoiceNumber`, etc.
- Numeric and alphanumeric IDs in notification variables

### 3. **How Validation Works**
- **API Call**: Makes HTTP GET request to QuickBooks API
- **Response**: Returns `{"hasAccess": true/false}` based on report ownership
- **Decision**: TRUE = continue workflow creation, FALSE = throw security exception

### 4. **After Validation**
- **Success**: Normal workflow creation proceeds (database save, Camunda deployment, AppConnect registration)
- **Failure**: Entire workflow creation is aborted, user gets 403 Forbidden error

## Configuration Integration

The validation is controlled by the configuration we added to `application-default.yaml`:

```yaml
report-access-validation:
  enabled: true  # Can be disabled to bypass validation
  baseUrl: https://sandbox-quickbooks.api.intuit.com
  reportValidationEndpoint: /v3/companyinfo/{realmId}/reports/{reportId}/validate
  timeoutMs: 5000
  maxRetries: 3
```

## Summary

Our implementation integrates seamlessly into the existing workflow creation flow:

1. **Early Validation**: Happens during placeholder processing, before any persistence
2. **API-Based**: Makes real API calls to verify report ownership
3. **Fail-Safe**: Blocks workflow creation if any report ID validation fails
4. **Transparent**: If validation passes, workflow creation continues exactly as before
5. **Configurable**: Can be enabled/disabled via configuration

The key insight is that we intercept the workflow creation process at the **placeholder extraction phase**, validate all report IDs via API calls, and either allow the workflow to continue normally or abort it with a security exception.
